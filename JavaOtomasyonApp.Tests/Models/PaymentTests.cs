using JavaOtomasyonApp.Models;
using Xunit;

namespace JavaOtomasyonApp.Tests.Models
{
    public class PaymentTests
    {
        [Fact]
        public void StatusDisplayName_Pending_ShouldReturnBeklemede()
        {
            // Arrange
            var payment = new Payment { Status = PaymentStatus.Pending };

            // Act
            var displayName = payment.StatusDisplayName;

            // Assert
            Assert.Equal("Beklemede", displayName);
        }

        [Fact]
        public void StatusDisplayName_Completed_ShouldReturnTamamlandi()
        {
            // Arrange
            var payment = new Payment { Status = PaymentStatus.Completed };

            // Act
            var displayName = payment.StatusDisplayName;

            // Assert
            Assert.Equal("Tamamlandı", displayName);
        }

        [Fact]
        public void StatusDisplayName_Failed_ShouldReturnBasarisiz()
        {
            // Arrange
            var payment = new Payment { Status = PaymentStatus.Failed };

            // Act
            var displayName = payment.StatusDisplayName;

            // Assert
            Assert.Equal("Başarısız", displayName);
        }

        [Fact]
        public void StatusDisplayName_Refunded_ShouldReturnIadeEdildi()
        {
            // Arrange
            var payment = new Payment { Status = PaymentStatus.Refunded };

            // Act
            var displayName = payment.StatusDisplayName;

            // Assert
            Assert.Equal("İade Edildi", displayName);
        }

        [Fact]
        public void FormattedAmount_ValidAmount_ShouldReturnFormattedString()
        {
            // Arrange
            var payment = new Payment { Amount = 149.99m };

            // Act
            var formattedAmount = payment.FormattedAmount;

            // Assert
            Assert.Contains("149,99", formattedAmount);
        }

        [Fact]
        public void IsCompleted_CompletedStatus_ShouldReturnTrue()
        {
            // Arrange
            var payment = new Payment { Status = PaymentStatus.Completed };

            // Act & Assert
            Assert.True(payment.IsCompleted);
        }

        [Theory]
        [InlineData(PaymentStatus.Pending)]
        [InlineData(PaymentStatus.Failed)]
        [InlineData(PaymentStatus.Refunded)]
        public void IsCompleted_NonCompletedStatus_ShouldReturnFalse(PaymentStatus status)
        {
            // Arrange
            var payment = new Payment { Status = status };

            // Act & Assert
            Assert.False(payment.IsCompleted);
        }

        [Fact]
        public void Payment_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange & Act
            var payment = new Payment();

            // Assert
            Assert.Equal(PaymentStatus.Pending, payment.Status);
            Assert.Equal("CreditCard", payment.PaymentMethod);
            Assert.True((DateTime.Now - payment.PaymentDate).TotalSeconds < 1);
        }
    }
}
