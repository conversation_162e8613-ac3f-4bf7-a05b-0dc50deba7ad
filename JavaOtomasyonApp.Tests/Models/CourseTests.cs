using JavaOtomasyonApp.Models;
using Xunit;

namespace JavaOtomasyonApp.Tests.Models
{
    public class CourseTests
    {
        [Fact]
        public void LevelDisplayName_BeginnerLevel_ShouldReturnBaslangic()
        {
            // Arrange
            var course = new Course { Level = CourseLevel.Beginner };

            // Act
            var displayName = course.LevelDisplayName;

            // Assert
            Assert.Equal("Başlangıç", displayName);
        }

        [Fact]
        public void LevelDisplayName_IntermediateLevel_ShouldReturnOrta()
        {
            // Arrange
            var course = new Course { Level = CourseLevel.Intermediate };

            // Act
            var displayName = course.LevelDisplayName;

            // Assert
            Assert.Equal("Orta", displayName);
        }

        [Fact]
        public void LevelDisplayName_AdvancedLevel_ShouldReturnIleri()
        {
            // Arrange
            var course = new Course { Level = CourseLevel.Advanced };

            // Act
            var displayName = course.LevelDisplayName;

            // Assert
            Assert.Equal("İleri", displayName);
        }

        [Fact]
        public void FormattedPrice_ValidPrice_ShouldReturnFormattedString()
        {
            // Arrange
            var course = new Course { Price = 99.99m };

            // Act
            var formattedPrice = course.FormattedPrice;

            // Assert
            Assert.Contains("99,99", formattedPrice);
        }

        [Fact]
        public void EstimatedDuration_ValidMinutes_ShouldReturnFormattedString()
        {
            // Arrange
            var course = new Course { EstimatedDurationMinutes = 120 };

            // Act
            var duration = course.EstimatedDuration;

            // Assert
            Assert.Equal("120 dakika", duration);
        }

        [Theory]
        [InlineData(30, "30 dakika")]
        [InlineData(60, "60 dakika")]
        [InlineData(90, "90 dakika")]
        [InlineData(120, "120 dakika")]
        public void EstimatedDuration_VariousMinutes_ShouldReturnCorrectFormat(int minutes, string expected)
        {
            // Arrange
            var course = new Course { EstimatedDurationMinutes = minutes };

            // Act
            var duration = course.EstimatedDuration;

            // Assert
            Assert.Equal(expected, duration);
        }
    }
}
