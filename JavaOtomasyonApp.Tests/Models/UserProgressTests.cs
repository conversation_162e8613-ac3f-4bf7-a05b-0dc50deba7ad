using JavaOtomasyonApp.Models;
using Xunit;

namespace JavaOtomasyonApp.Tests.Models
{
    public class UserProgressTests
    {
        [Fact]
        public void StatusDisplayName_NotStarted_ShouldReturnBaslanmadi()
        {
            // Arrange
            var progress = new UserProgress { Status = ProgressStatus.NotStarted };

            // Act
            var displayName = progress.StatusDisplayName;

            // Assert
            Assert.Equal("Başlanmadı", displayName);
        }

        [Fact]
        public void StatusDisplayName_InProgress_ShouldReturnDevamEdiyor()
        {
            // Arrange
            var progress = new UserProgress { Status = ProgressStatus.InProgress };

            // Act
            var displayName = progress.StatusDisplayName;

            // Assert
            Assert.Equal("Devam Ediyor", displayName);
        }

        [Fact]
        public void StatusDisplayName_Completed_ShouldReturnTamamlandi()
        {
            // Arrange
            var progress = new UserProgress { Status = ProgressStatus.Completed };

            // Act
            var displayName = progress.StatusDisplayName;

            // Assert
            Assert.Equal("Tamamlandı", displayName);
        }

        [Theory]
        [InlineData(30, "30 dakika")]
        [InlineData(60, "1 saat")]
        [InlineData(90, "1 saat 30 dakika")]
        [InlineData(120, "2 saat")]
        [InlineData(150, "2 saat 30 dakika")]
        public void FormattedTimeSpent_VariousMinutes_ShouldReturnCorrectFormat(int minutes, string expected)
        {
            // Arrange
            var progress = new UserProgress { TimeSpentMinutes = minutes };

            // Act
            var formattedTime = progress.FormattedTimeSpent;

            // Assert
            Assert.Equal(expected, formattedTime);
        }

        [Fact]
        public void IsCompleted_CompletedStatus_ShouldReturnTrue()
        {
            // Arrange
            var progress = new UserProgress { Status = ProgressStatus.Completed };

            // Act & Assert
            Assert.True(progress.IsCompleted);
        }

        [Fact]
        public void IsCompleted_NotCompletedStatus_ShouldReturnFalse()
        {
            // Arrange
            var progress = new UserProgress { Status = ProgressStatus.InProgress };

            // Act & Assert
            Assert.False(progress.IsCompleted);
        }

        [Fact]
        public void HasStarted_NotStartedStatus_ShouldReturnFalse()
        {
            // Arrange
            var progress = new UserProgress { Status = ProgressStatus.NotStarted };

            // Act & Assert
            Assert.False(progress.HasStarted);
        }

        [Theory]
        [InlineData(ProgressStatus.InProgress)]
        [InlineData(ProgressStatus.Completed)]
        public void HasStarted_StartedStatus_ShouldReturnTrue(ProgressStatus status)
        {
            // Arrange
            var progress = new UserProgress { Status = status };

            // Act & Assert
            Assert.True(progress.HasStarted);
        }
    }
}
