using JavaOtomasyonApp.Data;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Tests
{
    public class TestAppDbContext : AppDbContext
    {
        public TestAppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // Test ortamında SQLite yerine InMemory database kullan
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseInMemoryDatabase("TestDatabase");
            }
        }
    }
}
