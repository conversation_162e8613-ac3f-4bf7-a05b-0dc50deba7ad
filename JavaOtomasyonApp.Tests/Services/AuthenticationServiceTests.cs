using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace JavaOtomasyonApp.Tests.Services
{
    public class AuthenticationServiceTests : IDisposable
    {
        private readonly AppDbContext _context;

        public AuthenticationServiceTests()
        {
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new TestAppDbContext(options);
            _context.Database.EnsureCreated();
        }

        [Fact]
        public async Task RegisterAsync_ValidUser_ShouldReturnSuccess()
        {
            // Arrange
            var email = "<EMAIL>";
            var password = "password123";
            var firstName = "Test";
            var lastName = "User";

            // Act
            var result = await AuthenticationService.RegisterAsync(email, password, firstName, lastName);

            // Assert
            Assert.True(result.Success);
            Assert.Equal("Kayıt başarılı. Şimdi giriş yapabilirsiniz.", result.Message);

            // Verify user was created in database
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == email.ToLower());
            Assert.NotNull(user);
            Assert.Equal(firstName, user.FirstName);
            Assert.Equal(lastName, user.LastName);
            Assert.True(BCrypt.Net.BCrypt.Verify(password, user.PasswordHash));
        }

        [Fact]
        public async Task RegisterAsync_DuplicateEmail_ShouldReturnFailure()
        {
            // Arrange
            var email = "<EMAIL>";
            var existingUser = new User
            {
                Email = email.ToLower(),
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("password"),
                FirstName = "Existing",
                LastName = "User",
                Role = "User",
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            _context.Users.Add(existingUser);
            await _context.SaveChangesAsync();

            // Act
            var result = await AuthenticationService.RegisterAsync(email, "newpassword", "New", "User");

            // Assert
            Assert.False(result.Success);
            Assert.Equal("Bu e-posta adresi zaten kullanılıyor.", result.Message);
        }

        [Fact]
        public async Task RegisterAsync_ShortPassword_ShouldReturnFailure()
        {
            // Arrange
            var email = "<EMAIL>";
            var shortPassword = "123";

            // Act
            var result = await AuthenticationService.RegisterAsync(email, shortPassword, "Test", "User");

            // Assert
            Assert.False(result.Success);
            Assert.Equal("Şifre en az 6 karakter olmalıdır.", result.Message);
        }

        [Fact]
        public async Task LoginAsync_ValidCredentials_ShouldReturnSuccess()
        {
            // Arrange
            var email = "<EMAIL>";
            var password = "password123";
            var user = new User
            {
                Email = email.ToLower(),
                PasswordHash = BCrypt.Net.BCrypt.HashPassword(password),
                FirstName = "Login",
                LastName = "User",
                Role = "User",
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Act
            var result = await AuthenticationService.LoginAsync(email, password);

            // Assert
            Assert.True(result.Success);
            Assert.Equal("Giriş başarılı.", result.Message);
            Assert.NotNull(AuthenticationService.CurrentUser);
            Assert.Equal(email.ToLower(), AuthenticationService.CurrentUser.Email);
        }

        [Fact]
        public async Task LoginAsync_InvalidEmail_ShouldReturnFailure()
        {
            // Act
            var result = await AuthenticationService.LoginAsync("<EMAIL>", "password");

            // Assert
            Assert.False(result.Success);
            Assert.Equal("E-posta adresi bulunamadı.", result.Message);
            Assert.Null(AuthenticationService.CurrentUser);
        }

        [Fact]
        public async Task LoginAsync_InvalidPassword_ShouldReturnFailure()
        {
            // Arrange
            var email = "<EMAIL>";
            var user = new User
            {
                Email = email.ToLower(),
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("correctpassword"),
                FirstName = "Wrong",
                LastName = "Pass",
                Role = "User",
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Act
            var result = await AuthenticationService.LoginAsync(email, "wrongpassword");

            // Assert
            Assert.False(result.Success);
            Assert.Equal("Şifre hatalı.", result.Message);
            Assert.Null(AuthenticationService.CurrentUser);
        }

        [Fact]
        public async Task LoginAsync_InactiveUser_ShouldReturnFailure()
        {
            // Arrange
            var email = "<EMAIL>";
            var user = new User
            {
                Email = email.ToLower(),
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("password"),
                FirstName = "Inactive",
                LastName = "User",
                Role = "User",
                IsActive = false,
                CreatedDate = DateTime.Now
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Act
            var result = await AuthenticationService.LoginAsync(email, "password");

            // Assert
            Assert.False(result.Success);
            Assert.Equal("Hesabınız deaktif durumda. Lütfen yönetici ile iletişime geçin.", result.Message);
        }

        [Fact]
        public void Logout_ShouldClearCurrentUser()
        {
            // Arrange
            AuthenticationService.CurrentUser = new User { Id = 1, Email = "<EMAIL>" };

            // Act
            AuthenticationService.Logout();

            // Assert
            Assert.Null(AuthenticationService.CurrentUser);
            Assert.False(AuthenticationService.IsLoggedIn);
        }

        [Fact]
        public void IsAdmin_AdminUser_ShouldReturnTrue()
        {
            // Arrange
            AuthenticationService.CurrentUser = new User { Role = "Admin" };

            // Act & Assert
            Assert.True(AuthenticationService.IsAdmin);
        }

        [Fact]
        public void IsAdmin_RegularUser_ShouldReturnFalse()
        {
            // Arrange
            AuthenticationService.CurrentUser = new User { Role = "User" };

            // Act & Assert
            Assert.False(AuthenticationService.IsAdmin);
        }

        public void Dispose()
        {
            AuthenticationService.Logout();
            _context.Dispose();
        }
    }
}
