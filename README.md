# Java Öğrenme Otomasyonu

Bu proje, Java programlama dilini öğrenmek isteyenler için geliştirilmiş kapsamlı bir masaüstü uygulamasıdır. C# WPF teknolojisi kullanılarak geliştirilmiştir.

## 🚀 Özellikler

### ✅ Fonksiyonel Gereksinimler

#### 1. Kullanıcı Yönetimi
- ✅ Kullanıcı kayıt sistemi (e-posta ile kayıt/şifre belirleme)
- ✅ Giriş yapma (authentication)
- ✅ Şifre sıfırlama mekanizması
- ✅ Profil yönetimi

#### 2. Admin Paneli
- ✅ Ders ekleme, silme ve güncelleme
- ✅ Seviyelere göre ders yönetimi (Beginner / Intermediate / Advanced)
- ✅ Kullanıcıları listeleme ve yönetme
- ✅ Ödeme geçmişi raporu
- ✅ Sistem istatistikleri

#### 3. Ders Yönetimi
- ✅ Dersler seviye bazlı gruplandırılm<PERSON>ş (Beginner, Intermediate, Advanced)
- ✅ Her dersin adı, açı<PERSON><PERSON><PERSON> ve içeriği mevcut
- ✅ Derslerin kilidini açmak için "Ödeme Yap" sistemi
- ✅ Kullanıcının hangi dersleri tamamladığı takip ediliyor

#### 4. Ödeme Sistemi
- ✅ Her ders için ödeme yapılması sistemi (simülasyon)
- ✅ Ödeme onayı sonrası dersin erişime açılması
- ✅ Ödeme geçmişinin kullanıcıya gösterilmesi
- ✅ İşlem numarası ve detaylı ödeme bilgileri

#### 5. Ana Sayfa / Dashboard
- ✅ Kullanıcının ilerlemesini gösteren panel
- ✅ Ders tamamlama istatistikleri
- ✅ Son aktiviteler listesi
- ✅ Hızlı erişim butonları

#### 6. İlerleme Takibi
- ✅ Kullanıcının ders ilerlemesi yüzdelik olarak gösteriliyor
- ✅ Tamamlanan dersler işaretleniyor
- ✅ Çalışma süresi takibi
- ✅ Ders durumu (Başlanmadı, Devam Ediyor, Tamamlandı)

### ⚙️ Teknik Özellikler

#### Uygulama Özellikleri
- ✅ C# WPF masaüstü uygulaması
- ✅ Modern ve kullanıcı dostu arayüz
- ✅ Offline çalışabilir (SQLite veritabanı)
- ✅ Responsive tasarım

#### Veritabanı
- ✅ SQLite veritabanı (LocalApplicationData klasöründe)
- ✅ Entity Framework Core kullanımı
- ✅ Kullanıcı bilgileri, ders içerikleri, ödeme durumu tabloları
- ✅ Otomatik veritabanı oluşturma ve seed data

#### Güvenlik
- ✅ Şifreler BCrypt ile hashlenmiş
- ✅ Yetkilendirme: Admin ve Normal Kullanıcı rolleri
- ✅ Güvenli authentication sistemi
- ✅ Session yönetimi

## 📁 Proje Yapısı

```
JavaOtomasyonApp/
├── Data/
│   └── AppDbContext.cs          # Entity Framework DbContext
├── Models/
│   ├── User.cs                  # Kullanıcı modeli
│   ├── Course.cs                # Ders modeli
│   ├── Payment.cs               # Ödeme modeli
│   └── UserProgress.cs          # Kullanıcı ilerleme modeli
├── Services/
│   └── AuthenticationService.cs # Kimlik doğrulama servisi
├── Views/
│   ├── LoginWindow.xaml         # Giriş ekranı
│   ├── RegisterWindow.xaml      # Kayıt ekranı
│   ├── MainWindow.xaml          # Ana pencere
│   ├── Pages/
│   │   ├── DashboardPage.xaml   # Dashboard sayfası
│   │   ├── CoursesPage.xaml     # Dersler sayfası
│   │   ├── ProgressPage.xaml    # İlerleme sayfası
│   │   ├── PaymentsPage.xaml    # Ödemeler sayfası
│   │   └── AdminPage.xaml       # Admin paneli
│   └── Windows/
│       ├── CourseDetailWindow.xaml # Ders detay penceresi
│       └── ProfileWindow.xaml   # Profil ayarları penceresi
├── App.xaml                     # Uygulama kaynakları ve stiller
└── JavaOtomasyonApp.csproj      # Proje dosyası
```

## 🛠️ Kurulum ve Çalıştırma

### Gereksinimler
- .NET 6.0 SDK veya üzeri
- Windows 10/11 (WPF uygulaması)

### Kurulum Adımları

1. **Projeyi klonlayın:**
   ```bash
   git clone <repository-url>
   cd java-otomasyon
   ```

2. **NuGet paketlerini yükleyin:**
   ```bash
   dotnet restore JavaOtomasyonApp/JavaOtomasyonApp.csproj
   ```

3. **Uygulamayı çalıştırın:**
   ```bash
   dotnet run --project JavaOtomasyonApp/JavaOtomasyonApp.csproj
   ```

### İlk Çalıştırma

Uygulama ilk çalıştırıldığında:
- SQLite veritabanı otomatik olarak oluşturulur
- Örnek dersler eklenir
- Varsayılan admin kullanıcısı oluşturulur:
  - **E-posta:** <EMAIL>
  - **Şifre:** admin123

## 👤 Kullanım

### Normal Kullanıcı İşlemleri
1. **Kayıt Olma:** Yeni hesap oluşturun
2. **Giriş Yapma:** E-posta ve şifre ile giriş yapın
3. **Dersler:** Mevcut dersleri görüntüleyin ve satın alın
4. **İlerleme:** Ders ilerlemelerinizi takip edin
5. **Ödemeler:** Ödeme geçmişinizi görüntüleyin
6. **Profil:** Kişisel bilgilerinizi güncelleyin

### Admin İşlemleri
1. **Admin Girişi:** <EMAIL> / admin123
2. **Ders Yönetimi:** Yeni ders ekleyin, mevcut dersleri düzenleyin
3. **Kullanıcı Yönetimi:** Tüm kullanıcıları görüntüleyin
4. **Raporlar:** Sistem istatistiklerini ve raporları görüntüleyin

## 🔧 Geliştirme

### Kullanılan Teknolojiler
- **C# 10.0**
- **WPF (Windows Presentation Foundation)**
- **Entity Framework Core 6.0**
- **SQLite**
- **BCrypt.Net** (Şifre hashleme)

### Veritabanı Şeması
- **Users:** Kullanıcı bilgileri
- **Courses:** Ders bilgileri
- **Payments:** Ödeme kayıtları
- **UserProgresses:** Kullanıcı ders ilerlemeleri

## 📝 Notlar

- Bu uygulama eğitim amaçlı geliştirilmiştir
- Ödeme sistemi simülasyon amaçlıdır, gerçek ödeme işlemi yapılmaz
- Veritabanı dosyası `%LocalAppData%\JavaOtomasyon\database.db` konumunda saklanır
- Uygulama offline çalışır, internet bağlantısı gerektirmez

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/AmazingFeature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add some AmazingFeature'`)
4. Branch'inizi push edin (`git push origin feature/AmazingFeature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
