using Microsoft.EntityFrameworkCore;
using JavaOtomasyonApp.Models;

namespace JavaOtomasyonApp.Data
{
    public class AppDbContext : DbContext
    {
        public DbSet<User> Users { get; set; }
        public DbSet<Course> Courses { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<UserProgress> UserProgresses { get; set; }

        public AppDbContext() : base()
        {
        }

        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), 
                                    "JavaOtomasyon", "database.db");
            
            // Klasör yoksa oluştur
            var directory = Path.GetDirectoryName(dbPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            optionsBuilder.UseSqlite($"Data Source={dbPath}");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // User entity configuration
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Email).IsRequired();
                entity.Property(e => e.PasswordHash).IsRequired();
                entity.Property(e => e.Role).HasDefaultValue("User");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
            });

            // Course entity configuration
            modelBuilder.Entity<Course>(entity =>
            {
                entity.Property(e => e.Price).HasColumnType("decimal(10,2)");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.Level).HasConversion<int>();
            });

            // Payment entity configuration
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.Property(e => e.Amount).HasColumnType("decimal(10,2)");
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.PaymentDate).HasDefaultValueSql("datetime('now')");
                
                entity.HasOne(d => d.User)
                    .WithMany(p => p.Payments)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Course)
                    .WithMany(p => p.Payments)
                    .HasForeignKey(d => d.CourseId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // UserProgress entity configuration
            modelBuilder.Entity<UserProgress>(entity =>
            {
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.ProgressPercentage).HasDefaultValue(0);
                entity.Property(e => e.TimeSpentMinutes).HasDefaultValue(0);

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserProgresses)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Course)
                    .WithMany(p => p.UserProgresses)
                    .HasForeignKey(d => d.CourseId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Bir kullanıcı bir kursa sadece bir kez kayıt olabilir
                entity.HasIndex(e => new { e.UserId, e.CourseId }).IsUnique();
            });

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Örnek kurslar
            modelBuilder.Entity<Course>().HasData(
                new Course
                {
                    Id = 1,
                    Title = "Java Temelleri",
                    Description = "Java programlama diline giriş, temel kavramlar ve syntax",
                    Content = "Bu kursta Java programlama dilinin temellerini öğreneceksiniz...",
                    Level = CourseLevel.Beginner,
                    Price = 99.99m,
                    OrderIndex = 1,
                    EstimatedDurationMinutes = 120,
                    Prerequisites = "Programlama deneyimi gerekmez"
                },
                new Course
                {
                    Id = 2,
                    Title = "Nesne Yönelimli Programlama",
                    Description = "Java'da OOP kavramları, sınıflar, nesneler ve kalıtım",
                    Content = "Bu kursta Java'da nesne yönelimli programlama prensiplerini öğreneceksiniz...",
                    Level = CourseLevel.Beginner,
                    Price = 149.99m,
                    OrderIndex = 2,
                    EstimatedDurationMinutes = 180,
                    Prerequisites = "Java Temelleri kursunu tamamlamış olmak"
                },
                new Course
                {
                    Id = 3,
                    Title = "Java Collections Framework",
                    Description = "ArrayList, HashMap, Set ve diğer koleksiyon türleri",
                    Content = "Bu kursta Java Collections Framework'ünü detaylı olarak öğreneceksiniz...",
                    Level = CourseLevel.Intermediate,
                    Price = 199.99m,
                    OrderIndex = 3,
                    EstimatedDurationMinutes = 150,
                    Prerequisites = "Nesne Yönelimli Programlama kursunu tamamlamış olmak"
                }
            );
        }
    }
}
