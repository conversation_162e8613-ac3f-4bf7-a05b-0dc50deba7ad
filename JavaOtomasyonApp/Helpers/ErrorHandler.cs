using System.Diagnostics;
using System.IO;
using System.Text;

namespace JavaOtomasyonApp.Helpers
{
    public static class ErrorHandler
    {
        private static readonly string LogDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
            "JavaOtomasyon", "Logs");

        private static readonly string LogFilePath = Path.Combine(LogDirectory, 
            $"error_log_{DateTime.Now:yyyyMMdd}.txt");

        static ErrorHandler()
        {
            // Log klasörünü oluştur
            if (!Directory.Exists(LogDirectory))
            {
                Directory.CreateDirectory(LogDirectory);
            }
        }

        /// <summary>
        /// Hata loglar ve kullanıcıya uygun mesaj gösterir
        /// </summary>
        /// <param name="exception">Hata</param>
        /// <param name="userMessage">Kullanıcıya gösterilecek mesaj</param>
        /// <param name="context"><PERSON>a bağlamı</param>
        public static void HandleError(Exception exception, string userMessage = null, string context = null)
        {
            try
            {
                // Hata logla
                LogError(exception, context);

                // Kullanıcıya mesaj göster
                var message = userMessage ?? GetUserFriendlyMessage(exception);
                ShowErrorToUser(message, exception);
            }
            catch (Exception logException)
            {
                // Log yazma hatası durumunda debug'a yaz
                Debug.WriteLine($"Error logging failed: {logException.Message}");
                Debug.WriteLine($"Original error: {exception.Message}");
            }
        }

        /// <summary>
        /// Async işlemler için hata yönetimi
        /// </summary>
        /// <param name="operation">Çalıştırılacak işlem</param>
        /// <param name="errorMessage">Hata durumunda gösterilecek mesaj</param>
        /// <param name="context">İşlem bağlamı</param>
        /// <returns>İşlem başarılı mı</returns>
        public static async Task<bool> TryExecuteAsync(Func<Task> operation, string errorMessage = null, string context = null)
        {
            try
            {
                await operation();
                return true;
            }
            catch (Exception ex)
            {
                HandleError(ex, errorMessage, context);
                return false;
            }
        }

        /// <summary>
        /// Sonuç döndüren async işlemler için hata yönetimi
        /// </summary>
        /// <typeparam name="T">Sonuç tipi</typeparam>
        /// <param name="operation">Çalıştırılacak işlem</param>
        /// <param name="defaultValue">Hata durumunda döndürülecek varsayılan değer</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <param name="context">İşlem bağlamı</param>
        /// <returns>İşlem sonucu veya varsayılan değer</returns>
        public static async Task<T> TryExecuteAsync<T>(Func<Task<T>> operation, T defaultValue = default, 
            string errorMessage = null, string context = null)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                HandleError(ex, errorMessage, context);
                return defaultValue;
            }
        }

        /// <summary>
        /// Senkron işlemler için hata yönetimi
        /// </summary>
        /// <param name="operation">Çalıştırılacak işlem</param>
        /// <param name="errorMessage">Hata mesajı</param>
        /// <param name="context">İşlem bağlamı</param>
        /// <returns>İşlem başarılı mı</returns>
        public static bool TryExecute(Action operation, string errorMessage = null, string context = null)
        {
            try
            {
                operation();
                return true;
            }
            catch (Exception ex)
            {
                HandleError(ex, errorMessage, context);
                return false;
            }
        }

        /// <summary>
        /// Hatayı dosyaya loglar
        /// </summary>
        /// <param name="exception">Hata</param>
        /// <param name="context">Bağlam</param>
        private static void LogError(Exception exception, string context)
        {
            var logEntry = new StringBuilder();
            logEntry.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR");
            logEntry.AppendLine($"Context: {context ?? "Unknown"}");
            logEntry.AppendLine($"Message: {exception.Message}");
            logEntry.AppendLine($"Type: {exception.GetType().Name}");
            
            if (exception.InnerException != null)
            {
                logEntry.AppendLine($"Inner Exception: {exception.InnerException.Message}");
            }
            
            logEntry.AppendLine($"Stack Trace: {exception.StackTrace}");
            logEntry.AppendLine(new string('-', 80));

            // Dosyaya yaz
            File.AppendAllText(LogFilePath, logEntry.ToString(), Encoding.UTF8);

            // Debug'a da yaz
            Debug.WriteLine(logEntry.ToString());
        }

        /// <summary>
        /// Kullanıcı dostu hata mesajı oluşturur
        /// </summary>
        /// <param name="exception">Hata</param>
        /// <returns>Kullanıcı dostu mesaj</returns>
        private static string GetUserFriendlyMessage(Exception exception)
        {
            return exception switch
            {
                UnauthorizedAccessException => "Bu işlem için yetkiniz bulunmuyor.",
                FileNotFoundException => "Gerekli dosya bulunamadı.",
                DirectoryNotFoundException => "Gerekli klasör bulunamadı.",
                IOException => "Dosya işlemi sırasında hata oluştu.",
                InvalidOperationException => "Geçersiz işlem gerçekleştirilmeye çalışıldı.",
                ArgumentException => "Geçersiz parametre değeri.",
                TimeoutException => "İşlem zaman aşımına uğradı.",
                System.Data.Common.DbException => "Veritabanı işlemi sırasında hata oluştu.",
                System.Net.NetworkInformation.NetworkInformationException => "Ağ bağlantısı hatası.",
                OutOfMemoryException => "Yetersiz bellek. Lütfen uygulamayı yeniden başlatın.",
                _ => "Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin."
            };
        }

        /// <summary>
        /// Kullanıcıya hata mesajı gösterir
        /// </summary>
        /// <param name="message">Mesaj</param>
        /// <param name="exception">Hata (detay için)</param>
        private static void ShowErrorToUser(string message, Exception exception)
        {
            var fullMessage = message;

            // Debug modunda detay ekle
            #if DEBUG
            fullMessage += $"\n\nDetay (Debug): {exception.Message}";
            #endif

            // Console uygulaması için
            Console.WriteLine($"❌ HATA: {fullMessage}");
        }

        /// <summary>
        /// Log dosyalarını temizler (30 günden eski olanları siler)
        /// </summary>
        public static void CleanupOldLogs()
        {
            try
            {
                if (!Directory.Exists(LogDirectory)) return;

                var files = Directory.GetFiles(LogDirectory, "error_log_*.txt");
                var cutoffDate = DateTime.Now.AddDays(-30);

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Log cleanup failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Uygulama başlangıcında çağrılacak başlatma metodu
        /// </summary>
        public static void Initialize()
        {
            // Eski logları temizle
            CleanupOldLogs();

            // Global exception handler ekle
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                if (e.ExceptionObject is Exception ex)
                {
                    HandleError(ex, "Kritik uygulama hatası oluştu.", "UnhandledException");
                }
            };

            // Console uygulaması için ek handler
            Console.WriteLine("✅ Hata yönetimi başlatıldı.");
        }

        /// <summary>
        /// Hata raporunu string olarak döndürür
        /// </summary>
        /// <returns>Hata raporu</returns>
        public static string GetErrorReport()
        {
            try
            {
                if (!File.Exists(LogFilePath))
                    return "Henüz hata kaydı bulunmuyor.";

                var content = File.ReadAllText(LogFilePath, Encoding.UTF8);
                return string.IsNullOrEmpty(content) ? "Log dosyası boş." : content;
            }
            catch (Exception ex)
            {
                return $"Hata raporu okunamadı: {ex.Message}";
            }
        }
    }
}
