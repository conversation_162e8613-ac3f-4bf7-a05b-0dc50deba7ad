using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using JavaOtomasyonApp.Helpers;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("========================================");
            Console.WriteLine("    Java Öğrenme Otomasyonu v1.0");
            Console.WriteLine("========================================");
            Console.WriteLine();

            // Hata yönetimini başlat
            ErrorHandler.Initialize();

            // Veritabanını başlat
            await InitializeDatabase();

            // Ana menüyü göster
            await ShowMainMenu();
        }

        static async Task InitializeDatabase()
        {
            Console.WriteLine("Veritabanı başlatılıyor...");
            
            try
            {
                using var context = new AppDbContext();
                await context.Database.EnsureCreatedAsync();

                // Varsayılan admin kullanıcısı oluştur
                if (!await context.Users.AnyAsync(u => u.Role == "Admin"))
                {
                    var adminUser = new User
                    {
                        Email = "<EMAIL>",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                        FirstName = "Admin",
                        LastName = "User",
                        Role = "Admin",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    };
                    context.Users.Add(adminUser);
                    await context.SaveChangesAsync();
                    Console.WriteLine("✅ Admin kullanıcısı oluşturuldu (<EMAIL> / admin123)");
                }

                Console.WriteLine("✅ Veritabanı hazır!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Veritabanı hatası: {ex.Message}");
                Environment.Exit(1);
            }
        }

        static async Task ShowMainMenu()
        {
            while (true)
            {
                Console.WriteLine();
                Console.WriteLine("=== ANA MENÜ ===");
                
                if (AuthenticationService.IsLoggedIn)
                {
                    Console.WriteLine($"Hoş geldin, {AuthenticationService.CurrentUser?.FirstName}!");
                    Console.WriteLine();
                    Console.WriteLine("1. Dersler");
                    Console.WriteLine("2. İlerleme");
                    Console.WriteLine("3. Ödemeler");
                    Console.WriteLine("4. Profil");
                    
                    if (AuthenticationService.IsAdmin)
                    {
                        Console.WriteLine("5. Admin Panel");
                    }
                    
                    Console.WriteLine("9. Çıkış Yap");
                    Console.WriteLine("0. Uygulamadan Çık");
                }
                else
                {
                    Console.WriteLine("1. Giriş Yap");
                    Console.WriteLine("2. Kayıt Ol");
                    Console.WriteLine("0. Çık");
                }

                Console.Write("\nSeçiminiz: ");
                var choice = Console.ReadLine();

                try
                {
                    if (AuthenticationService.IsLoggedIn)
                    {
                        await HandleLoggedInMenu(choice);
                    }
                    else
                    {
                        await HandleGuestMenu(choice);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Hata: {ex.Message}");
                }
            }
        }

        static async Task HandleGuestMenu(string choice)
        {
            switch (choice)
            {
                case "1":
                    await LoginMenu();
                    break;
                case "2":
                    await RegisterMenu();
                    break;
                case "0":
                    Console.WriteLine("Güle güle!");
                    Environment.Exit(0);
                    break;
                default:
                    Console.WriteLine("❌ Geçersiz seçim!");
                    break;
            }
        }

        static async Task HandleLoggedInMenu(string choice)
        {
            switch (choice)
            {
                case "1":
                    await ShowCourses();
                    break;
                case "2":
                    await ShowProgress();
                    break;
                case "3":
                    await ShowPayments();
                    break;
                case "4":
                    await ShowProfile();
                    break;
                case "5" when AuthenticationService.IsAdmin:
                    await ShowAdminPanel();
                    break;
                case "9":
                    AuthenticationService.Logout();
                    Console.WriteLine("✅ Çıkış yapıldı!");
                    break;
                case "0":
                    Console.WriteLine("Güle güle!");
                    Environment.Exit(0);
                    break;
                default:
                    Console.WriteLine("❌ Geçersiz seçim!");
                    break;
            }
        }

        static async Task LoginMenu()
        {
            Console.WriteLine("\n=== GİRİŞ YAP ===");
            Console.Write("E-posta: ");
            var email = Console.ReadLine();
            Console.Write("Şifre: ");
            var password = ReadPassword();

            var result = await AuthenticationService.LoginAsync(email, password);
            
            if (result.Success)
            {
                Console.WriteLine($"✅ {result.Message}");
            }
            else
            {
                Console.WriteLine($"❌ {result.Message}");
            }
        }

        static async Task RegisterMenu()
        {
            Console.WriteLine("\n=== KAYIT OL ===");
            Console.Write("Ad: ");
            var firstName = Console.ReadLine();
            Console.Write("Soyad: ");
            var lastName = Console.ReadLine();
            Console.Write("E-posta: ");
            var email = Console.ReadLine();
            Console.Write("Şifre: ");
            var password = ReadPassword();

            var result = await AuthenticationService.RegisterAsync(email, password, firstName, lastName);
            
            if (result.Success)
            {
                Console.WriteLine($"✅ {result.Message}");
            }
            else
            {
                Console.WriteLine($"❌ {result.Message}");
            }
        }

        static async Task ShowCourses()
        {
            Console.WriteLine("\n=== DERSLER ===");
            
            using var context = new AppDbContext();
            var courses = await context.Courses
                .Where(c => c.IsActive)
                .OrderBy(c => c.Level)
                .ThenBy(c => c.OrderIndex)
                .ToListAsync();

            if (!courses.Any())
            {
                Console.WriteLine("Henüz ders bulunmuyor.");
                return;
            }

            foreach (var course in courses)
            {
                Console.WriteLine($"\n📚 {course.Title}");
                Console.WriteLine($"   Seviye: {course.LevelDisplayName}");
                Console.WriteLine($"   Fiyat: {course.FormattedPrice}");
                Console.WriteLine($"   Süre: {course.EstimatedDuration}");
                Console.WriteLine($"   Açıklama: {course.Description}");
                
                // Kullanıcının bu dersi satın alıp almadığını kontrol et
                var hasPurchased = await context.Payments
                    .AnyAsync(p => p.UserId == AuthenticationService.CurrentUser.Id && 
                                  p.CourseId == course.Id && 
                                  p.Status == PaymentStatus.Completed);
                
                if (hasPurchased)
                {
                    Console.WriteLine("   ✅ Satın alınmış");
                }
                else
                {
                    Console.WriteLine("   🔒 Satın alınmamış");
                }
            }
        }

        static async Task ShowProgress()
        {
            Console.WriteLine("\n=== İLERLEME ===");
            
            using var context = new AppDbContext();
            var progresses = await context.UserProgresses
                .Where(up => up.UserId == AuthenticationService.CurrentUser.Id)
                .Include(up => up.Course)
                .ToListAsync();

            if (!progresses.Any())
            {
                Console.WriteLine("Henüz ders satın almamışsınız.");
                return;
            }

            foreach (var progress in progresses)
            {
                Console.WriteLine($"\n📖 {progress.Course.Title}");
                Console.WriteLine($"   Durum: {progress.StatusDisplayName}");
                Console.WriteLine($"   İlerleme: %{progress.ProgressPercentage}");
                Console.WriteLine($"   Çalışma Süresi: {progress.FormattedTimeSpent}");
            }
        }

        static async Task ShowPayments()
        {
            Console.WriteLine("\n=== ÖDEMELER ===");
            
            using var context = new AppDbContext();
            var payments = await context.Payments
                .Where(p => p.UserId == AuthenticationService.CurrentUser.Id)
                .Include(p => p.Course)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();

            if (!payments.Any())
            {
                Console.WriteLine("Henüz ödeme yapılmamış.");
                return;
            }

            foreach (var payment in payments)
            {
                Console.WriteLine($"\n💳 {payment.Course.Title}");
                Console.WriteLine($"   Tutar: {payment.FormattedAmount}");
                Console.WriteLine($"   Durum: {payment.StatusDisplayName}");
                Console.WriteLine($"   Tarih: {payment.PaymentDate:dd.MM.yyyy HH:mm}");
                Console.WriteLine($"   İşlem No: {payment.TransactionId}");
            }
        }

        static async Task ShowProfile()
        {
            Console.WriteLine("\n=== PROFİL ===");
            var user = AuthenticationService.CurrentUser;
            
            Console.WriteLine($"Ad Soyad: {user.FullName}");
            Console.WriteLine($"E-posta: {user.Email}");
            Console.WriteLine($"Rol: {user.Role}");
            Console.WriteLine($"Üyelik Tarihi: {user.CreatedDate:dd.MM.yyyy}");
            
            if (user.LastLoginDate.HasValue)
            {
                Console.WriteLine($"Son Giriş: {user.LastLoginDate:dd.MM.yyyy HH:mm}");
            }
        }

        static async Task ShowAdminPanel()
        {
            Console.WriteLine("\n=== ADMİN PANEL ===");
            
            using var context = new AppDbContext();
            
            var totalUsers = await context.Users.CountAsync();
            var totalCourses = await context.Courses.CountAsync();
            var totalRevenue = await context.Payments
                .Where(p => p.Status == PaymentStatus.Completed)
                .SumAsync(p => p.Amount);
            
            Console.WriteLine($"Toplam Kullanıcı: {totalUsers}");
            Console.WriteLine($"Toplam Ders: {totalCourses}");
            Console.WriteLine($"Toplam Gelir: ₺{totalRevenue:N2}");
        }

        static string ReadPassword()
        {
            var password = "";
            ConsoleKeyInfo key;
            
            do
            {
                key = Console.ReadKey(true);
                
                if (key.Key != ConsoleKey.Backspace && key.Key != ConsoleKey.Enter)
                {
                    password += key.KeyChar;
                    Console.Write("*");
                }
                else if (key.Key == ConsoleKey.Backspace && password.Length > 0)
                {
                    password = password[0..^1];
                    Console.Write("\b \b");
                }
            }
            while (key.Key != ConsoleKey.Enter);
            
            Console.WriteLine();
            return password;
        }
    }
}
