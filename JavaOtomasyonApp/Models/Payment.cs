using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JavaOtomasyonApp.Models
{
    public enum PaymentStatus
    {
        Pending = 1,
        Completed = 2,
        Failed = 3,
        Refunded = 4
    }

    public class Payment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("User")]
        public int UserId { get; set; }

        [Required]
        [ForeignKey("Course")]
        public int CourseId { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;

        [Required]
        public DateTime PaymentDate { get; set; } = DateTime.Now;

        [MaxLength(100)]
        public string? TransactionId { get; set; }

        [MaxLength(50)]
        public string PaymentMethod { get; set; } = "CreditCard";

        [MaxLength(500)]
        public string? Notes { get; set; }

        public DateTime? CompletedDate { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Course Course { get; set; } = null!;

        // Computed Properties
        public string StatusDisplayName => Status switch
        {
            PaymentStatus.Pending => "Beklemede",
            PaymentStatus.Completed => "Tamamlandı",
            PaymentStatus.Failed => "Başarısız",
            PaymentStatus.Refunded => "İade Edildi",
            _ => "Bilinmiyor"
        };

        public string FormattedAmount => $"{Amount:C2}";
        public bool IsCompleted => Status == PaymentStatus.Completed;
    }
}
