using System.ComponentModel.DataAnnotations;

namespace JavaOtomasyonApp.Models
{
    public enum CourseLevel
    {
        Beginner = 1,
        Intermediate = 2,
        Advanced = 3
    }

    public class Course
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        public string Content { get; set; } = string.Empty;

        [Required]
        public CourseLevel Level { get; set; }

        [Required]
        public decimal Price { get; set; }

        [Required]
        public bool IsActive { get; set; } = true;

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        [Range(1, int.MaxValue)]
        public int OrderIndex { get; set; } = 1;

        public int EstimatedDurationMinutes { get; set; } = 60;

        [MaxLength(500)]
        public string? ImagePath { get; set; }

        [MaxLength(1000)]
        public string? Prerequisites { get; set; }

        // Navigation Properties
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<UserProgress> UserProgresses { get; set; } = new List<UserProgress>();

        // Computed Properties
        public string LevelDisplayName => Level switch
        {
            CourseLevel.Beginner => "Başlangıç",
            CourseLevel.Intermediate => "Orta",
            CourseLevel.Advanced => "İleri",
            _ => "Bilinmiyor"
        };

        public string FormattedPrice => $"{Price:C2}";
        public string EstimatedDuration => $"{EstimatedDurationMinutes} dakika";
    }
}
