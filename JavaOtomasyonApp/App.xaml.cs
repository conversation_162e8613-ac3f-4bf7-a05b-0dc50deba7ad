using System.Windows;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Helpers;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Hata yönetimini başlat
            ErrorHandler.Initialize();

            // Veritabanını başlat
            using (var context = new AppDbContext())
            {
                context.Database.EnsureCreated();
                
                // Varsayılan admin kullanıcısı oluştur
                if (!context.Users.Any(u => u.Role == "Admin"))
                {
                    var adminUser = new User
                    {
                        Email = "<EMAIL>",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                        FirstName = "Admin",
                        LastName = "User",
                        Role = "Admin",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    };
                    context.Users.Add(adminUser);
                    context.SaveChanges();
                }
            }
        }
    }
}
