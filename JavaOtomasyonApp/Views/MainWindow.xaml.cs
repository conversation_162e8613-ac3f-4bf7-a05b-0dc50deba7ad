using System.Windows;
using JavaOtomasyonApp.Services;
using JavaOtomasyonApp.Views.Pages;

namespace JavaOtomasyonApp.Views
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // Kullanıcı bilgilerini göster
            if (AuthenticationService.CurrentUser != null)
            {
                WelcomeTextBlock.Text = $"Hoş geldin, {AuthenticationService.CurrentUser.FirstName}!";
                
                // Admin paneli sadece admin kullanıcılar için görünür
                if (AuthenticationService.IsAdmin)
                {
                    AdminButton.Visibility = Visibility.Visible;
                }
            }

            // Varsayılan olarak Dashboard sayfasını göster
            NavigateToPage(new DashboardPage());
            UpdateStatus("Dashboard yüklendi");
        }

        private void DashboardButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToPage(new DashboardPage());
            UpdateStatus("Dashboard");
        }

        private void CoursesButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToPage(new CoursesPage());
            UpdateStatus("Dersler");
        }

        private void ProgressButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToPage(new ProgressPage());
            UpdateStatus("İlerleme");
        }

        private void PaymentsButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToPage(new PaymentsPage());
            UpdateStatus("Ödemeler");
        }

        private void AdminButton_Click(object sender, RoutedEventArgs e)
        {
            if (AuthenticationService.IsAdmin)
            {
                NavigateToPage(new AdminPage());
                UpdateStatus("Admin Panel");
            }
            else
            {
                MessageBox.Show("Bu sayfaya erişim yetkiniz yok.", "Erişim Reddedildi", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void ProfileButton_Click(object sender, RoutedEventArgs e)
        {
            var profileWindow = new ProfileWindow();
            profileWindow.Owner = this;
            profileWindow.ShowDialog();
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("Çıkış yapmak istediğinizden emin misiniz?", 
                                       "Çıkış Onayı", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                AuthenticationService.Logout();
                
                var loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            }
        }

        private void NavigateToPage(object page)
        {
            try
            {
                MainFrame.Content = page;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Sayfa yüklenirken hata oluştu: {ex.Message}", 
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatus(string status)
        {
            StatusTextBlock.Text = status;
        }

        // Public navigation methods for other pages to use
        public void NavigateToCoursesPage()
        {
            NavigateToPage(new CoursesPage());
            UpdateStatus("Dersler");
        }

        public void NavigateToProgressPage()
        {
            NavigateToPage(new ProgressPage());
            UpdateStatus("İlerleme");
        }

        public void NavigateToPaymentsPage()
        {
            NavigateToPage(new PaymentsPage());
            UpdateStatus("Ödemeler");
        }

        protected override void OnClosed(EventArgs e)
        {
            base.OnClosed(e);
            Application.Current.Shutdown();
        }
    }
}
