using System.Windows;
using System.Windows.Media;
using JavaOtomasyonApp.Services;

namespace JavaOtomasyonApp.Views
{
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(EmailTextBox.Text) || 
                string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                ShowMessage("Lütfen e-posta ve şifre alanlarını doldurun.", false);
                return;
            }

            LoginButton.IsEnabled = false;
            LoginButton.Content = "Giriş yapılıyor...";

            try
            {
                var result = await AuthenticationService.LoginAsync(
                    EmailTextBox.Text.Trim(), 
                    PasswordBox.Password);

                if (result.Success)
                {
                    ShowMessage(result.Message, true);
                    
                    // Ana pencereyi aç
                    var mainWindow = new MainWindow();
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    ShowMessage(result.Message, false);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"Beklenmeyen hata: {ex.Message}", false);
            }
            finally
            {
                LoginButton.IsEnabled = true;
                LoginButton.Content = "Giriş Yap";
            }
        }

        private void RegisterButton_Click(object sender, RoutedEventArgs e)
        {
            var registerWindow = new RegisterWindow();
            registerWindow.Owner = this;
            registerWindow.ShowDialog();
        }

        private async void ForgotPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                ShowMessage("Lütfen e-posta adresinizi girin.", false);
                return;
            }

            ForgotPasswordButton.IsEnabled = false;
            
            try
            {
                var result = await AuthenticationService.ResetPasswordAsync(EmailTextBox.Text.Trim());
                ShowMessage(result.Message, result.Success);
            }
            catch (Exception ex)
            {
                ShowMessage($"Hata: {ex.Message}", false);
            }
            finally
            {
                ForgotPasswordButton.IsEnabled = true;
            }
        }

        private void ShowMessage(string message, bool isSuccess)
        {
            MessageTextBlock.Text = message;
            MessageTextBlock.Foreground = isSuccess ? 
                new SolidColorBrush(Colors.Green) : 
                new SolidColorBrush(Colors.Red);
        }

        private void Window_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
        }
    }
}
