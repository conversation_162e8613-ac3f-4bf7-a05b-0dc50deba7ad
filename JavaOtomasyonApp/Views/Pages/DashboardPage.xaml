<Page x:Class="JavaOtomasyonApp.Views.Pages.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="Dashboard">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Welcome Section -->
            <Border Grid.Row="0" Background="#007ACC" CornerRadius="10" Padding="30" Margin="0,0,0,30">
                <StackPanel>
                    <TextBlock x:Name="WelcomeTextBlock" 
                              Text="Hoş Geldiniz!" 
                              FontSize="28" FontWeight="Bold" 
                              Foreground="White" Margin="0,0,0,10"/>
                    <TextBlock x:Name="MotivationTextBlock" 
                              Text="Java öğrenme yolculuğunuzda bugün de ilerleme kaydedin!" 
                              FontSize="16" Foreground="White" TextWrapping="Wrap"/>
                </StackPanel>
            </Border>

            <!-- Statistics Cards -->
            <Grid Grid.Row="1" Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Courses Card -->
                <Border Grid.Column="0" Background="White" CornerRadius="10" 
                       Padding="20" Margin="0,0,10,0" 
                       BorderBrush="#E0E0E0" BorderThickness="1">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📚" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock x:Name="TotalCoursesTextBlock" Text="0" 
                                  FontSize="24" FontWeight="Bold" 
                                  HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="Toplam Ders" FontSize="12" 
                                  Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Completed Courses Card -->
                <Border Grid.Column="1" Background="White" CornerRadius="10" 
                       Padding="20" Margin="5,0" 
                       BorderBrush="#E0E0E0" BorderThickness="1">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock x:Name="CompletedCoursesTextBlock" Text="0" 
                                  FontSize="24" FontWeight="Bold" 
                                  HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="Tamamlanan" FontSize="12" 
                                  Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- In Progress Card -->
                <Border Grid.Column="2" Background="White" CornerRadius="10" 
                       Padding="20" Margin="5,0" 
                       BorderBrush="#E0E0E0" BorderThickness="1">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="⏳" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock x:Name="InProgressCoursesTextBlock" Text="0" 
                                  FontSize="24" FontWeight="Bold" 
                                  HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="Devam Eden" FontSize="12" 
                                  Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Total Time Card -->
                <Border Grid.Column="3" Background="White" CornerRadius="10" 
                       Padding="20" Margin="10,0,0,0" 
                       BorderBrush="#E0E0E0" BorderThickness="1">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="⏰" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock x:Name="TotalTimeTextBlock" Text="0h" 
                                  FontSize="24" FontWeight="Bold" 
                                  HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="Toplam Süre" FontSize="12" 
                                  Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Quick Actions -->
            <Border Grid.Row="2" Background="White" CornerRadius="10" 
                   Padding="30" Margin="0,0,0,30"
                   BorderBrush="#E0E0E0" BorderThickness="1">
                <StackPanel>
                    <TextBlock Text="Hızlı İşlemler" Style="{StaticResource SubtitleTextStyle}"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button x:Name="ViewCoursesButton" Content="Dersleri Görüntüle" 
                               Style="{StaticResource ModernButtonStyle}"
                               Margin="0,0,15,0" Click="ViewCoursesButton_Click"/>
                        
                        <Button x:Name="ContinueLearningButton" Content="Öğrenmeye Devam Et" 
                               Background="#28A745" Foreground="White"
                               BorderThickness="0" Padding="15,8" FontSize="14"
                               Cursor="Hand" Margin="0,0,15,0"
                               Click="ContinueLearningButton_Click"/>
                        
                        <Button x:Name="ViewProgressButton" Content="İlerlemeyi Gör" 
                               Background="#17A2B8" Foreground="White"
                               BorderThickness="0" Padding="15,8" FontSize="14"
                               Cursor="Hand" Click="ViewProgressButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Recent Activity -->
            <Border Grid.Row="3" Background="White" CornerRadius="10" 
                   Padding="30" BorderBrush="#E0E0E0" BorderThickness="1">
                <StackPanel>
                    <TextBlock Text="Son Aktiviteler" Style="{StaticResource SubtitleTextStyle}"/>
                    
                    <ListView x:Name="RecentActivityListView" 
                             BorderThickness="0" Background="Transparent"
                             ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#F8F9FA" CornerRadius="5" 
                                       Padding="15" Margin="0,5">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" Text="{Binding Icon}" 
                                                  FontSize="20" VerticalAlignment="Center" 
                                                  Margin="0,0,15,0"/>
                                        
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding Title}" FontWeight="SemiBold"/>
                                            <TextBlock Text="{Binding Description}" 
                                                      FontSize="12" Foreground="#666666"/>
                                        </StackPanel>
                                        
                                        <TextBlock Grid.Column="2" Text="{Binding Time}" 
                                                  FontSize="12" Foreground="#666666" 
                                                  VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </StackPanel>
            </Border>
        </Grid>
    </ScrollViewer>
</Page>
