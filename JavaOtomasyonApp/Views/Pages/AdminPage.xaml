<Page x:Class="JavaOtomasyonApp.Views.Pages.AdminPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="Admin Panel">

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,30">
            <TextBlock Text="⚙️ Admin Panel" Style="{StaticResource TitleTextStyle}"/>
            <TextBlock Text="Sistem yönetimi ve raporlama araçları" 
                      FontSize="16" Foreground="#666666"/>
        </StackPanel>

        <!-- Admin Actions -->
        <Border Grid.Row="1" Background="White" CornerRadius="10" 
               Padding="30" Margin="0,0,0,30"
               BorderBrush="#E0E0E0" BorderThickness="1">
            <StackPanel>
                <TextBlock Text="Hızlı İşlemler" Style="{StaticResource SubtitleTextStyle}"/>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="ManageCoursesButton" Content="📚 Ders Yönetimi" 
                           Style="{StaticResource ModernButtonStyle}"
                           Margin="0,0,15,0" Click="ManageCoursesButton_Click"/>
                    
                    <Button x:Name="ManageUsersButton" Content="👥 Kullanıcı Yönetimi" 
                           Background="#28A745" Foreground="White"
                           BorderThickness="0" Padding="15,8" FontSize="14"
                           Cursor="Hand" Margin="0,0,15,0"
                           Click="ManageUsersButton_Click"/>
                    
                    <Button x:Name="ViewReportsButton" Content="📊 Raporlar" 
                           Background="#17A2B8" Foreground="White"
                           BorderThickness="0" Padding="15,8" FontSize="14"
                           Cursor="Hand" Click="ViewReportsButton_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Tab Control for Admin Functions -->
        <TabControl Grid.Row="2" Background="White" BorderThickness="0">
            <TabItem Header="📚 Ders Yönetimi" FontSize="14" Padding="15,10">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <!-- Add New Course -->
                        <Border Background="#F8F9FA" CornerRadius="10" 
                               Padding="20" Margin="0,0,0,20">
                            <StackPanel>
                                <TextBlock Text="Yeni Ders Ekle" FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Ders Adı:" 
                                              FontWeight="SemiBold" Margin="0,0,10,5"/>
                                    <TextBox Grid.Row="1" Grid.Column="0" x:Name="CourseTitleTextBox" 
                                            Style="{StaticResource ModernTextBoxStyle}" 
                                            Margin="0,0,10,15"/>

                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="Fiyat (₺):" 
                                              FontWeight="SemiBold" Margin="10,0,0,5"/>
                                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="CoursePriceTextBox" 
                                            Style="{StaticResource ModernTextBoxStyle}" 
                                            Margin="10,0,0,15"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Seviye:" 
                                              FontWeight="SemiBold" Margin="0,0,10,5"/>
                                    <ComboBox Grid.Row="3" Grid.Column="0" x:Name="CourseLevelComboBox" 
                                             Padding="10,8" FontSize="14" Margin="0,0,10,15">
                                        <ComboBoxItem Content="Başlangıç" Tag="Beginner"/>
                                        <ComboBoxItem Content="Orta" Tag="Intermediate"/>
                                        <ComboBoxItem Content="İleri" Tag="Advanced"/>
                                    </ComboBox>

                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="Süre (dakika):" 
                                              FontWeight="SemiBold" Margin="10,0,0,5"/>
                                    <TextBox Grid.Row="3" Grid.Column="1" x:Name="CourseDurationTextBox" 
                                            Style="{StaticResource ModernTextBoxStyle}" 
                                            Margin="10,0,0,15"/>

                                    <TextBlock Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="2" 
                                              Text="Açıklama:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="2" 
                                            x:Name="CourseDescriptionTextBox" 
                                            Style="{StaticResource ModernTextBoxStyle}" 
                                            Height="60" TextWrapping="Wrap" 
                                            AcceptsReturn="True" Margin="0,0,0,15"/>
                                </Grid>

                                <Button x:Name="AddCourseButton" Content="Ders Ekle" 
                                       Style="{StaticResource ModernButtonStyle}"
                                       HorizontalAlignment="Left"
                                       Click="AddCourseButton_Click"/>
                            </StackPanel>
                        </Border>

                        <!-- Existing Courses -->
                        <Border Background="White" CornerRadius="10" 
                               Padding="20" BorderBrush="#E0E0E0" BorderThickness="1">
                            <StackPanel>
                                <TextBlock Text="Mevcut Dersler" FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/>
                                
                                <DataGrid x:Name="CoursesDataGrid" 
                                         AutoGenerateColumns="False" 
                                         IsReadOnly="True"
                                         GridLinesVisibility="None"
                                         HeadersVisibility="Column"
                                         Background="Transparent"
                                         BorderThickness="0"
                                         RowBackground="Transparent"
                                         AlternatingRowBackground="#F8F9FA"
                                         MaxHeight="300">
                                    
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="50"/>
                                        <DataGridTextColumn Header="Ders Adı" Binding="{Binding Title}" Width="*"/>
                                        <DataGridTextColumn Header="Seviye" Binding="{Binding LevelDisplayName}" Width="100"/>
                                        <DataGridTextColumn Header="Fiyat" Binding="{Binding FormattedPrice}" Width="100"/>
                                        <DataGridTextColumn Header="Süre" Binding="{Binding EstimatedDuration}" Width="100"/>
                                        <DataGridCheckBoxColumn Header="Aktif" Binding="{Binding IsActive}" Width="60"/>
                                        
                                        <DataGridTemplateColumn Header="İşlemler" Width="150">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal">
                                                        <Button Content="Düzenle" 
                                                               Background="#FFC107" Foreground="White"
                                                               BorderThickness="0" Padding="6,3" FontSize="11"
                                                               Cursor="Hand" Margin="0,0,5,0"
                                                               Tag="{Binding Id}"
                                                               Click="EditCourseButton_Click"/>
                                                        <Button Content="Sil" 
                                                               Background="#DC3545" Foreground="White"
                                                               BorderThickness="0" Padding="6,3" FontSize="11"
                                                               Cursor="Hand" Tag="{Binding Id}"
                                                               Click="DeleteCourseButton_Click"/>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <TabItem Header="👥 Kullanıcılar" FontSize="14" Padding="15,10">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <Border Background="White" CornerRadius="10" 
                               Padding="20" BorderBrush="#E0E0E0" BorderThickness="1">
                            <StackPanel>
                                <TextBlock Text="Kullanıcı Listesi" FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/>
                                
                                <DataGrid x:Name="UsersDataGrid" 
                                         AutoGenerateColumns="False" 
                                         IsReadOnly="True"
                                         GridLinesVisibility="None"
                                         HeadersVisibility="Column"
                                         Background="Transparent"
                                         BorderThickness="0"
                                         RowBackground="Transparent"
                                         AlternatingRowBackground="#F8F9FA"
                                         MaxHeight="400">
                                    
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="50"/>
                                        <DataGridTextColumn Header="Ad Soyad" Binding="{Binding FullName}" Width="*"/>
                                        <DataGridTextColumn Header="E-posta" Binding="{Binding Email}" Width="*"/>
                                        <DataGridTextColumn Header="Rol" Binding="{Binding Role}" Width="80"/>
                                        <DataGridCheckBoxColumn Header="Aktif" Binding="{Binding IsActive}" Width="60"/>
                                        <DataGridTextColumn Header="Kayıt Tarihi" 
                                                          Binding="{Binding CreatedDate, StringFormat=dd.MM.yyyy}" Width="120"/>
                                        <DataGridTextColumn Header="Son Giriş" 
                                                          Binding="{Binding LastLoginDate, StringFormat=dd.MM.yyyy}" Width="120"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <TabItem Header="📊 Raporlar" FontSize="14" Padding="15,10">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <!-- Statistics Cards -->
                        <Grid Margin="0,0,0,30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Border Grid.Column="0" Background="White" CornerRadius="10" 
                                   Padding="20" Margin="0,0,10,0" 
                                   BorderBrush="#E0E0E0" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="👥" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock x:Name="TotalUsersTextBlock" Text="0" 
                                              FontSize="20" FontWeight="Bold" 
                                              HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                    <TextBlock Text="Toplam Kullanıcı" FontSize="12" 
                                              Foreground="#666666" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="1" Background="White" CornerRadius="10" 
                                   Padding="20" Margin="5,0" 
                                   BorderBrush="#E0E0E0" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📚" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock x:Name="TotalCoursesReportTextBlock" Text="0" 
                                              FontSize="20" FontWeight="Bold" 
                                              HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                    <TextBlock Text="Toplam Ders" FontSize="12" 
                                              Foreground="#666666" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="2" Background="White" CornerRadius="10" 
                                   Padding="20" Margin="5,0" 
                                   BorderBrush="#E0E0E0" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock x:Name="TotalRevenueTextBlock" Text="₺0" 
                                              FontSize="20" FontWeight="Bold" 
                                              HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                    <TextBlock Text="Toplam Gelir" FontSize="12" 
                                              Foreground="#666666" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="3" Background="White" CornerRadius="10" 
                                   Padding="20" Margin="10,0,0,0" 
                                   BorderBrush="#E0E0E0" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📈" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock x:Name="TotalSalesTextBlock" Text="0" 
                                              FontSize="20" FontWeight="Bold" 
                                              HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                    <TextBlock Text="Toplam Satış" FontSize="12" 
                                              Foreground="#666666" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- Recent Payments -->
                        <Border Background="White" CornerRadius="10" 
                               Padding="20" BorderBrush="#E0E0E0" BorderThickness="1">
                            <StackPanel>
                                <TextBlock Text="Son Ödemeler" FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/>
                                
                                <DataGrid x:Name="RecentPaymentsDataGrid" 
                                         AutoGenerateColumns="False" 
                                         IsReadOnly="True"
                                         GridLinesVisibility="None"
                                         HeadersVisibility="Column"
                                         Background="Transparent"
                                         BorderThickness="0"
                                         RowBackground="Transparent"
                                         AlternatingRowBackground="#F8F9FA"
                                         MaxHeight="300">
                                    
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="İşlem No" Binding="{Binding TransactionId}" Width="100"/>
                                        <DataGridTextColumn Header="Kullanıcı" Binding="{Binding User.FullName}" Width="*"/>
                                        <DataGridTextColumn Header="Ders" Binding="{Binding Course.Title}" Width="*"/>
                                        <DataGridTextColumn Header="Tutar" Binding="{Binding FormattedAmount}" Width="100"/>
                                        <DataGridTextColumn Header="Tarih" 
                                                          Binding="{Binding PaymentDate, StringFormat=dd.MM.yyyy HH:mm}" Width="140"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </Grid>
</Page>
