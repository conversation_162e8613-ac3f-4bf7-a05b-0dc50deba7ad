<Page x:Class="JavaOtomasyonApp.Views.Pages.PaymentsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="Ödemeler">

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,30">
            <TextBlock Text="Ödeme Geçmişim" Style="{StaticResource TitleTextStyle}"/>
            <TextBlock Text="Satın aldığınız derslerin ödeme bilgilerini görüntüleyin." 
                      FontSize="16" Foreground="#666666"/>
        </StackPanel>

        <!-- Payment Summary -->
        <Border Grid.Row="1" Background="White" CornerRadius="10" 
               Padding="30" Margin="0,0,0,30"
               BorderBrush="#E0E0E0" BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Payments -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="💳" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="Toplam Ödeme" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                    <TextBlock x:Name="TotalPaymentTextBlock" Text="₺0,00" 
                              FontSize="24" FontWeight="Bold" 
                              Foreground="#007ACC" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Successful Payments -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="Başarılı Ödemeler" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                    <TextBlock x:Name="SuccessfulPaymentsTextBlock" Text="0" 
                              FontSize="24" FontWeight="Bold" 
                              Foreground="#28A745" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Purchased Courses -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="📚" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="Satın Alınan Dersler" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                    <TextBlock x:Name="PurchasedCoursesTextBlock" Text="0" 
                              FontSize="24" FontWeight="Bold" 
                              Foreground="#17A2B8" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Payments List -->
        <Border Grid.Row="2" Background="White" CornerRadius="10" 
               Padding="30" BorderBrush="#E0E0E0" BorderThickness="1">
            <StackPanel>
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="Ödeme Geçmişi" Style="{StaticResource SubtitleTextStyle}"/>
                    
                    <Button Grid.Column="1" Content="🔄 Yenile" 
                           Background="Transparent" BorderBrush="#007ACC" 
                           BorderThickness="1" Padding="10,5" FontSize="12"
                           Cursor="Hand" Click="RefreshButton_Click"/>
                </Grid>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="400">
                    <DataGrid x:Name="PaymentsDataGrid" 
                             AutoGenerateColumns="False" 
                             IsReadOnly="True"
                             GridLinesVisibility="None"
                             HeadersVisibility="Column"
                             Background="Transparent"
                             BorderThickness="0"
                             RowBackground="Transparent"
                             AlternatingRowBackground="#F8F9FA">
                        
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="İşlem No" 
                                              Binding="{Binding TransactionId}" 
                                              Width="100"/>
                            
                            <DataGridTextColumn Header="Ders" 
                                              Binding="{Binding Course.Title}" 
                                              Width="*"/>
                            
                            <DataGridTextColumn Header="Tutar" 
                                              Binding="{Binding FormattedAmount}" 
                                              Width="100"/>
                            
                            <DataGridTextColumn Header="Durum" 
                                              Binding="{Binding StatusDisplayName}" 
                                              Width="100"/>
                            
                            <DataGridTextColumn Header="Ödeme Tarihi" 
                                              Binding="{Binding PaymentDate, StringFormat=dd.MM.yyyy HH:mm}" 
                                              Width="140"/>
                            
                            <DataGridTextColumn Header="Ödeme Yöntemi" 
                                              Binding="{Binding PaymentMethod}" 
                                              Width="120"/>
                            
                            <DataGridTemplateColumn Header="İşlemler" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Content="Detay" 
                                               Background="#17A2B8" Foreground="White"
                                               BorderThickness="0" Padding="8,4" FontSize="11"
                                               Cursor="Hand" Tag="{Binding Id}"
                                               Click="PaymentDetailButton_Click"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>

                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow">
                                <Setter Property="Padding" Value="0,8"/>
                                <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                <Setter Property="BorderBrush" Value="#E0E0E0"/>
                            </Style>
                        </DataGrid.RowStyle>

                        <DataGrid.ColumnHeaderStyle>
                            <Style TargetType="DataGridColumnHeader">
                                <Setter Property="Background" Value="#F8F9FA"/>
                                <Setter Property="Foreground" Value="#333333"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Padding" Value="10,8"/>
                                <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                <Setter Property="BorderBrush" Value="#E0E0E0"/>
                            </Style>
                        </DataGrid.ColumnHeaderStyle>
                    </DataGrid>
                </ScrollViewer>

                <!-- No Payments Message -->
                <Border x:Name="NoPaymentsMessage" 
                       Background="#F8F9FA" CornerRadius="10" 
                       Padding="40" Visibility="Collapsed">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="💳" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        <TextBlock Text="Henüz ödeme yapılmamış" 
                                  FontSize="18" FontWeight="Bold" 
                                  HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="İlk dersinizi satın alarak Java öğrenme yolculuğunuza başlayın!" 
                                  FontSize="14" Foreground="#666666" 
                                  HorizontalAlignment="Center" TextWrapping="Wrap" Margin="0,0,0,20"/>
                        <Button Content="Dersleri Görüntüle" 
                               Style="{StaticResource ModernButtonStyle}"
                               Click="ViewCoursesButton_Click"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>
    </Grid>
</Page>
