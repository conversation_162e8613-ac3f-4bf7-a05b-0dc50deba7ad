using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using JavaOtomasyonApp.Views.Windows;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Pages
{
    public partial class CoursesPage : Page
    {
        public ObservableCollection<Course> Courses { get; set; }
        private List<Course> _allCourses = new List<Course>();

        public CoursesPage()
        {
            InitializeComponent();
            Courses = new ObservableCollection<Course>();
            CoursesItemsControl.ItemsSource = Courses;
            LoadCourses();
        }

        private async void LoadCourses()
        {
            try
            {
                ShowLoading(true);

                using var context = new AppDbContext();
                _allCourses = await context.Courses
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Level)
                    .ThenBy(c => c.OrderIndex)
                    .ToListAsync();

                ApplyFilter();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Dersler yüklenirken hata oluştu: {ex.Message}", 
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void LevelFilter_Changed(object sender, RoutedEventArgs e)
        {
            ApplyFilter();
        }

        private void ApplyFilter()
        {
            Courses.Clear();

            var filteredCourses = _allCourses.AsEnumerable();

            if (BeginnerRadio.IsChecked == true)
                filteredCourses = filteredCourses.Where(c => c.Level == CourseLevel.Beginner);
            else if (IntermediateRadio.IsChecked == true)
                filteredCourses = filteredCourses.Where(c => c.Level == CourseLevel.Intermediate);
            else if (AdvancedRadio.IsChecked == true)
                filteredCourses = filteredCourses.Where(c => c.Level == CourseLevel.Advanced);

            foreach (var course in filteredCourses)
            {
                Courses.Add(course);
            }

            // Ders bulunamadı mesajını göster/gizle
            NoCoursesMessage.Visibility = Courses.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
        }

        private void ViewDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int courseId)
            {
                var course = _allCourses.FirstOrDefault(c => c.Id == courseId);
                if (course != null)
                {
                    var detailWindow = new CourseDetailWindow(course);
                    detailWindow.Owner = Window.GetWindow(this);
                    detailWindow.ShowDialog();
                }
            }
        }

        private async void PurchaseButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int courseId)
            {
                var course = _allCourses.FirstOrDefault(c => c.Id == courseId);
                if (course == null) return;

                // Kullanıcının bu dersi zaten satın alıp almadığını kontrol et
                if (await HasUserPurchasedCourse(courseId))
                {
                    MessageBox.Show("Bu dersi zaten satın almışsınız!", 
                                  "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = MessageBox.Show(
                    $"'{course.Title}' dersini {course.FormattedPrice} karşılığında satın almak istediğinizden emin misiniz?",
                    "Satın Alma Onayı",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    await ProcessPurchase(course);
                }
            }
        }

        private async Task<bool> HasUserPurchasedCourse(int courseId)
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return false;

                using var context = new AppDbContext();
                return await context.Payments
                    .AnyAsync(p => p.UserId == AuthenticationService.CurrentUser.Id && 
                                  p.CourseId == courseId && 
                                  p.Status == PaymentStatus.Completed);
            }
            catch
            {
                return false;
            }
        }

        private async Task ProcessPurchase(Course course)
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                using var context = new AppDbContext();

                // Ödeme kaydı oluştur
                var payment = new Payment
                {
                    UserId = AuthenticationService.CurrentUser.Id,
                    CourseId = course.Id,
                    Amount = course.Price,
                    Status = PaymentStatus.Completed, // Simülasyon için direkt tamamlandı
                    PaymentDate = DateTime.Now,
                    CompletedDate = DateTime.Now,
                    TransactionId = Guid.NewGuid().ToString("N")[..10].ToUpper(),
                    PaymentMethod = "CreditCard",
                    Notes = "Simülasyon ödemesi"
                };

                context.Payments.Add(payment);

                // Kullanıcı ilerleme kaydı oluştur
                var userProgress = new UserProgress
                {
                    UserId = AuthenticationService.CurrentUser.Id,
                    CourseId = course.Id,
                    Status = ProgressStatus.NotStarted,
                    ProgressPercentage = 0,
                    StartedDate = null,
                    CompletedDate = null,
                    LastAccessedDate = DateTime.Now,
                    TimeSpentMinutes = 0
                };

                context.UserProgresses.Add(userProgress);
                await context.SaveChangesAsync();

                MessageBox.Show(
                    $"'{course.Title}' dersi başarıyla satın alındı!\n\nİşlem No: {payment.TransactionId}\nTutar: {payment.FormattedAmount}",
                    "Satın Alma Başarılı",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Satın alma işlemi sırasında hata oluştu: {ex.Message}",
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowLoading(bool show)
        {
            LoadingBorder.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            CoursesItemsControl.Visibility = show ? Visibility.Collapsed : Visibility.Visible;
        }
    }
}
