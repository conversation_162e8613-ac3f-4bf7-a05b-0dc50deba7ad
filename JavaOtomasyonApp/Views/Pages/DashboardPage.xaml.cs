using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Pages
{
    public partial class DashboardPage : Page
    {
        public ObservableCollection<ActivityItem> RecentActivities { get; set; }

        public DashboardPage()
        {
            InitializeComponent();
            RecentActivities = new ObservableCollection<ActivityItem>();
            RecentActivityListView.ItemsSource = RecentActivities;
            LoadDashboardData();
        }

        private async void LoadDashboardData()
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                using var context = new AppDbContext();
                var userId = AuthenticationService.CurrentUser.Id;

                // İstatistikleri yükle
                var totalCourses = await context.Courses.CountAsync(c => c.IsActive);
                var userProgresses = await context.UserProgresses
                    .Where(up => up.UserId == userId)
                    .Include(up => up.Course)
                    .ToListAsync();

                var completedCourses = userProgresses.Count(up => up.Status == ProgressStatus.Completed);
                var inProgressCourses = userProgresses.Count(up => up.Status == ProgressStatus.InProgress);
                var totalTimeMinutes = userProgresses.Sum(up => up.TimeSpentMinutes);

                // UI'yi güncelle
                TotalCoursesTextBlock.Text = totalCourses.ToString();
                CompletedCoursesTextBlock.Text = completedCourses.ToString();
                InProgressCoursesTextBlock.Text = inProgressCourses.ToString();
                TotalTimeTextBlock.Text = FormatTime(totalTimeMinutes);

                // Hoş geldin mesajını güncelle
                WelcomeTextBlock.Text = $"Hoş Geldin, {AuthenticationService.CurrentUser.FirstName}!";

                // Son aktiviteleri yükle
                await LoadRecentActivities();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Dashboard verileri yüklenirken hata oluştu: {ex.Message}", 
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadRecentActivities()
        {
            try
            {
                RecentActivities.Clear();

                using var context = new AppDbContext();
                var userId = AuthenticationService.CurrentUser!.Id;

                // Son ödemeler
                var recentPayments = await context.Payments
                    .Where(p => p.UserId == userId && p.Status == PaymentStatus.Completed)
                    .Include(p => p.Course)
                    .OrderByDescending(p => p.CompletedDate)
                    .Take(3)
                    .ToListAsync();

                foreach (var payment in recentPayments)
                {
                    RecentActivities.Add(new ActivityItem
                    {
                        Icon = "💳",
                        Title = $"{payment.Course.Title} - Ödeme Tamamlandı",
                        Description = $"{payment.FormattedAmount} ödeme yapıldı",
                        Time = GetRelativeTime(payment.CompletedDate ?? payment.PaymentDate)
                    });
                }

                // Son tamamlanan dersler
                var recentCompletions = await context.UserProgresses
                    .Where(up => up.UserId == userId && up.Status == ProgressStatus.Completed)
                    .Include(up => up.Course)
                    .OrderByDescending(up => up.CompletedDate)
                    .Take(3)
                    .ToListAsync();

                foreach (var completion in recentCompletions)
                {
                    RecentActivities.Add(new ActivityItem
                    {
                        Icon = "🎉",
                        Title = $"{completion.Course.Title} Tamamlandı!",
                        Description = "Ders başarıyla tamamlandı",
                        Time = GetRelativeTime(completion.CompletedDate ?? DateTime.Now)
                    });
                }

                // Eğer aktivite yoksa varsayılan mesaj ekle
                if (!RecentActivities.Any())
                {
                    RecentActivities.Add(new ActivityItem
                    {
                        Icon = "🚀",
                        Title = "Öğrenme yolculuğunuza başlayın!",
                        Description = "İlk dersinizi satın alarak başlayabilirsiniz",
                        Time = "Şimdi"
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Son aktiviteler yüklenirken hata oluştu: {ex.Message}", 
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string FormatTime(int minutes)
        {
            if (minutes < 60)
                return $"{minutes}m";
            
            var hours = minutes / 60;
            var remainingMinutes = minutes % 60;
            
            if (remainingMinutes == 0)
                return $"{hours}h";
            
            return $"{hours}h {remainingMinutes}m";
        }

        private string GetRelativeTime(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;
            
            if (timeSpan.TotalMinutes < 1)
                return "Az önce";
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} dakika önce";
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} saat önce";
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays} gün önce";
            
            return dateTime.ToString("dd.MM.yyyy");
        }

        private void ViewCoursesButton_Click(object sender, RoutedEventArgs e)
        {
            // Ana penceredeki Dersler sayfasına yönlendir
            if (Window.GetWindow(this) is MainWindow mainWindow)
            {
                // MainWindow'daki CoursesButton_Click metodunu çağır
                mainWindow.NavigateToCoursesPage();
            }
        }

        private void ContinueLearningButton_Click(object sender, RoutedEventArgs e)
        {
            // İlerleme sayfasına yönlendir
            if (Window.GetWindow(this) is MainWindow mainWindow)
            {
                mainWindow.NavigateToProgressPage();
            }
        }

        private void ViewProgressButton_Click(object sender, RoutedEventArgs e)
        {
            // İlerleme sayfasına yönlendir
            if (Window.GetWindow(this) is MainWindow mainWindow)
            {
                mainWindow.NavigateToProgressPage();
            }
        }
    }

    public class ActivityItem
    {
        public string Icon { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Time { get; set; } = string.Empty;
    }
}
