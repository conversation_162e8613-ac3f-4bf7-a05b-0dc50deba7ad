<Page x:Class="JavaOtomasyonApp.Views.Pages.CoursesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="Dersler">

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,30">
            <TextBlock Text="Java Dersleri" Style="{StaticResource TitleTextStyle}"/>
            <TextBlock Text="Seviyenize uygun dersleri seçin ve Java öğrenme yolculuğunuza başlayın!" 
                      FontSize="16" Foreground="#666666" TextWrapping="Wrap"/>
        </StackPanel>

        <!-- Level Filter -->
        <Border Grid.Row="1" Background="White" CornerRadius="10" 
               Padding="20" Margin="0,0,0,20"
               BorderBrush="#E0E0E0" BorderThickness="1">
            <StackPanel>
                <TextBlock Text="Seviye Filtresi" FontWeight="SemiBold" Margin="0,0,0,10"/>
                <StackPanel Orientation="Horizontal">
                    <RadioButton x:Name="AllLevelsRadio" Content="Tümü" 
                               IsChecked="True" Margin="0,0,20,0"
                               Checked="LevelFilter_Changed"/>
                    <RadioButton x:Name="BeginnerRadio" Content="Başlangıç" 
                               Margin="0,0,20,0" Checked="LevelFilter_Changed"/>
                    <RadioButton x:Name="IntermediateRadio" Content="Orta" 
                               Margin="0,0,20,0" Checked="LevelFilter_Changed"/>
                    <RadioButton x:Name="AdvancedRadio" Content="İleri" 
                               Checked="LevelFilter_Changed"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Courses List -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <ItemsControl x:Name="CoursesItemsControl">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Background="White" CornerRadius="10" 
                               Padding="25" Margin="0,0,0,20"
                               BorderBrush="#E0E0E0" BorderThickness="1">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Course Icon -->
                                <Border Grid.Column="0" Background="#007ACC" 
                                       CornerRadius="50" Width="80" Height="80"
                                       Margin="0,0,20,0">
                                    <TextBlock Text="☕" FontSize="32" 
                                              Foreground="White" 
                                              HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"/>
                                </Border>

                                <!-- Course Info -->
                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                    <TextBlock Text="{Binding Title}" 
                                              FontSize="20" FontWeight="Bold" 
                                              Margin="0,0,0,5"/>
                                    
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                        <Border Background="#28A745" CornerRadius="12" 
                                               Padding="8,4" Margin="0,0,10,0">
                                            <TextBlock Text="{Binding LevelDisplayName}" 
                                                      Foreground="White" FontSize="12"/>
                                        </Border>
                                        <Border Background="#17A2B8" CornerRadius="12" 
                                               Padding="8,4" Margin="0,0,10,0">
                                            <TextBlock Text="{Binding EstimatedDuration}" 
                                                      Foreground="White" FontSize="12"/>
                                        </Border>
                                        <Border Background="#FFC107" CornerRadius="12" 
                                               Padding="8,4">
                                            <TextBlock Text="{Binding FormattedPrice}" 
                                                      Foreground="White" FontSize="12" FontWeight="Bold"/>
                                        </Border>
                                    </StackPanel>
                                    
                                    <TextBlock Text="{Binding Description}" 
                                              FontSize="14" Foreground="#666666" 
                                              TextWrapping="Wrap" MaxWidth="400"/>
                                    
                                    <TextBlock Text="{Binding Prerequisites}" 
                                              FontSize="12" Foreground="#999999" 
                                              FontStyle="Italic" Margin="0,5,0,0"
                                              TextWrapping="Wrap" MaxWidth="400"/>
                                </StackPanel>

                                <!-- Action Buttons -->
                                <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                    <Button Content="Detayları Gör" 
                                           Background="#6C757D" Foreground="White"
                                           BorderThickness="0" Padding="15,8" 
                                           FontSize="14" Cursor="Hand"
                                           Margin="0,0,0,10"
                                           Tag="{Binding Id}"
                                           Click="ViewDetailsButton_Click"/>
                                    
                                    <Button Content="Satın Al" 
                                           Style="{StaticResource ModernButtonStyle}"
                                           Tag="{Binding Id}"
                                           Click="PurchaseButton_Click"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- Loading Indicator -->
        <Border x:Name="LoadingBorder" Grid.Row="2" 
               Background="#80FFFFFF" Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="⏳" FontSize="48" HorizontalAlignment="Center"/>
                <TextBlock Text="Dersler yükleniyor..." 
                          FontSize="16" HorizontalAlignment="Center" 
                          Margin="0,10,0,0"/>
            </StackPanel>
        </Border>

        <!-- No Courses Message -->
        <Border x:Name="NoCoursesMessage" Grid.Row="2" 
               Background="White" CornerRadius="10" 
               Padding="50" Visibility="Collapsed"
               BorderBrush="#E0E0E0" BorderThickness="1">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="📚" FontSize="64" HorizontalAlignment="Center" 
                          Margin="0,0,0,20"/>
                <TextBlock Text="Henüz ders bulunamadı" 
                          FontSize="20" FontWeight="Bold" 
                          HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <TextBlock Text="Seçtiğiniz seviyede ders bulunmuyor. Lütfen farklı bir seviye seçin." 
                          FontSize="14" Foreground="#666666" 
                          HorizontalAlignment="Center" TextWrapping="Wrap"/>
            </StackPanel>
        </Border>
    </Grid>
</Page>
