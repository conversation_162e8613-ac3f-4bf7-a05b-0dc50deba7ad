using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Pages
{
    public partial class PaymentsPage : Page
    {
        public ObservableCollection<Payment> Payments { get; set; }

        public PaymentsPage()
        {
            InitializeComponent();
            Payments = new ObservableCollection<Payment>();
            PaymentsDataGrid.ItemsSource = Payments;
            LoadPaymentData();
        }

        private async void LoadPaymentData()
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                using var context = new AppDbContext();
                var userId = AuthenticationService.CurrentUser.Id;

                // Kullanıcının ödemelerini getir
                var payments = await context.Payments
                    .Where(p => p.UserId == userId)
                    .Include(p => p.Course)
                    .OrderByDescending(p => p.PaymentDate)
                    .ToListAsync();

                Payments.Clear();
                foreach (var payment in payments)
                {
                    Payments.Add(payment);
                }

                // İstatistikleri hesapla ve göster
                UpdateStatistics(payments);

                // Eğer hiç ödeme yoksa mesaj göster
                NoPaymentsMessage.Visibility = payments.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
                PaymentsDataGrid.Visibility = payments.Count == 0 ? Visibility.Collapsed : Visibility.Visible;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ödeme verileri yüklenirken hata oluştu: {ex.Message}",
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics(List<Payment> payments)
        {
            if (payments.Count == 0)
            {
                TotalPaymentTextBlock.Text = "₺0,00";
                SuccessfulPaymentsTextBlock.Text = "0";
                PurchasedCoursesTextBlock.Text = "0";
                return;
            }

            // Toplam ödeme tutarı (sadece başarılı ödemeler)
            var totalAmount = payments
                .Where(p => p.Status == PaymentStatus.Completed)
                .Sum(p => p.Amount);
            TotalPaymentTextBlock.Text = $"₺{totalAmount:N2}";

            // Başarılı ödeme sayısı
            var successfulCount = payments.Count(p => p.Status == PaymentStatus.Completed);
            SuccessfulPaymentsTextBlock.Text = successfulCount.ToString();

            // Satın alınan ders sayısı (başarılı ödemeler)
            var purchasedCourses = payments
                .Where(p => p.Status == PaymentStatus.Completed)
                .Select(p => p.CourseId)
                .Distinct()
                .Count();
            PurchasedCoursesTextBlock.Text = purchasedCourses.ToString();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadPaymentData();
        }

        private void PaymentDetailButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int paymentId)
            {
                var payment = Payments.FirstOrDefault(p => p.Id == paymentId);
                if (payment != null)
                {
                    ShowPaymentDetail(payment);
                }
            }
        }

        private void ShowPaymentDetail(Payment payment)
        {
            var detailMessage = $"Ödeme Detayları\n\n" +
                              $"İşlem No: {payment.TransactionId}\n" +
                              $"Ders: {payment.Course.Title}\n" +
                              $"Tutar: {payment.FormattedAmount}\n" +
                              $"Durum: {payment.StatusDisplayName}\n" +
                              $"Ödeme Tarihi: {payment.PaymentDate:dd.MM.yyyy HH:mm}\n" +
                              $"Tamamlanma Tarihi: {(payment.CompletedDate?.ToString("dd.MM.yyyy HH:mm") ?? "Henüz tamamlanmadı")}\n" +
                              $"Ödeme Yöntemi: {payment.PaymentMethod}\n";

            if (!string.IsNullOrWhiteSpace(payment.Notes))
            {
                detailMessage += $"Notlar: {payment.Notes}\n";
            }

            MessageBox.Show(detailMessage, "Ödeme Detayları", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ViewCoursesButton_Click(object sender, RoutedEventArgs e)
        {
            // Ana penceredeki Dersler sayfasına yönlendir
            if (Window.GetWindow(this) is MainWindow mainWindow)
            {
                mainWindow.NavigateToCoursesPage();
            }
        }
    }
}
