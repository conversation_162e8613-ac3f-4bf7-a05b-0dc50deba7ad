<Page x:Class="JavaOtomasyonApp.Views.Pages.ProgressPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="İlerleme">

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,30">
            <TextBlock Text="Öğrenme İlerlemem" Style="{StaticResource TitleTextStyle}"/>
            <TextBlock Text="Java öğrenme yolculuğunuzdaki ilerlemenizi takip edin!" 
                      FontSize="16" Foreground="#666666"/>
        </StackPanel>

        <!-- Progress Summary -->
        <Border Grid.Row="1" Background="White" CornerRadius="10" 
               Padding="30" Margin="0,0,0,30"
               BorderBrush="#E0E0E0" BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Overall Progress -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="Genel İlerleme" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                    <TextBlock x:Name="OverallProgressTextBlock" Text="0%" 
                              FontSize="24" FontWeight="Bold" 
                              Foreground="#007ACC" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Completed Courses -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="Tamamlanan Dersler" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                    <TextBlock x:Name="CompletedCoursesTextBlock" Text="0 / 0" 
                              FontSize="24" FontWeight="Bold" 
                              Foreground="#28A745" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Total Study Time -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="⏰" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="Toplam Çalışma Süresi" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                    <TextBlock x:Name="TotalStudyTimeTextBlock" Text="0 saat" 
                              FontSize="24" FontWeight="Bold" 
                              Foreground="#17A2B8" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Course Progress List -->
        <Border Grid.Row="2" Background="White" CornerRadius="10" 
               Padding="30" BorderBrush="#E0E0E0" BorderThickness="1">
            <StackPanel>
                <TextBlock Text="Ders İlerlemeleri" Style="{StaticResource SubtitleTextStyle}"/>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="400">
                    <ItemsControl x:Name="ProgressItemsControl">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#F8F9FA" CornerRadius="10" 
                                       Padding="20" Margin="0,0,0,15">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- Course Info -->
                                        <Grid Grid.Row="0" Margin="0,0,0,10">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="{Binding Course.Title}" 
                                                          FontSize="16" FontWeight="Bold" Margin="0,0,0,5"/>
                                                <StackPanel Orientation="Horizontal">
                                                    <Border Background="#007ACC" CornerRadius="10" 
                                                           Padding="6,3" Margin="0,0,10,0">
                                                        <TextBlock Text="{Binding Course.LevelDisplayName}" 
                                                                  Foreground="White" FontSize="11"/>
                                                    </Border>
                                                    <Border Background="#6C757D" CornerRadius="10" 
                                                           Padding="6,3">
                                                        <TextBlock Text="{Binding StatusDisplayName}" 
                                                                  Foreground="White" FontSize="11"/>
                                                    </Border>
                                                </StackPanel>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                                <TextBlock Text="{Binding ProgressPercentage, StringFormat={}{0}%}" 
                                                          FontSize="20" FontWeight="Bold" 
                                                          Foreground="#007ACC" HorizontalAlignment="Right"/>
                                                <TextBlock Text="{Binding FormattedTimeSpent}" 
                                                          FontSize="12" Foreground="#666666" 
                                                          HorizontalAlignment="Right"/>
                                            </StackPanel>
                                        </Grid>

                                        <!-- Progress Bar -->
                                        <ProgressBar Grid.Row="1" Height="8" 
                                                   Value="{Binding ProgressPercentage}" 
                                                   Maximum="100" Margin="0,0,0,10"
                                                   Background="#E0E0E0" Foreground="#007ACC"/>

                                        <!-- Action Buttons -->
                                        <StackPanel Grid.Row="2" Orientation="Horizontal">
                                            <Button Content="Devam Et" 
                                                   Background="#28A745" Foreground="White"
                                                   BorderThickness="0" Padding="12,6" FontSize="12"
                                                   Cursor="Hand" Margin="0,0,10,0"
                                                   Tag="{Binding CourseId}"
                                                   Click="ContinueButton_Click"/>
                                            
                                            <Button Content="Detaylar" 
                                                   Background="#17A2B8" Foreground="White"
                                                   BorderThickness="0" Padding="12,6" FontSize="12"
                                                   Cursor="Hand" Tag="{Binding CourseId}"
                                                   Click="DetailsButton_Click"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- No Progress Message -->
                <Border x:Name="NoProgressMessage" 
                       Background="#F8F9FA" CornerRadius="10" 
                       Padding="40" Visibility="Collapsed">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📚" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        <TextBlock Text="Henüz ders satın almamışsınız" 
                                  FontSize="18" FontWeight="Bold" 
                                  HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="Java öğrenme yolculuğunuza başlamak için dersler sayfasını ziyaret edin!" 
                                  FontSize="14" Foreground="#666666" 
                                  HorizontalAlignment="Center" TextWrapping="Wrap" Margin="0,0,0,20"/>
                        <Button Content="Dersleri Görüntüle" 
                               Style="{StaticResource ModernButtonStyle}"
                               Click="ViewCoursesButton_Click"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>
    </Grid>
</Page>
