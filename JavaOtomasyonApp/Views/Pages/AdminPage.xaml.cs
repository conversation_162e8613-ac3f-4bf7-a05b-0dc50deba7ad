using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Pages
{
    public partial class AdminPage : Page
    {
        public ObservableCollection<Course> Courses { get; set; }
        public ObservableCollection<User> Users { get; set; }
        public ObservableCollection<Payment> RecentPayments { get; set; }

        public AdminPage()
        {
            InitializeComponent();
            
            // Admin kontrolü
            if (!AuthenticationService.IsAdmin)
            {
                MessageBox.Show("Bu sayfaya erişim yetkiniz yok.", "<PERSON><PERSON><PERSON><PERSON>dedil<PERSON>", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            Courses = new ObservableCollection<Course>();
            Users = new ObservableCollection<User>();
            RecentPayments = new ObservableCollection<Payment>();

            CoursesDataGrid.ItemsSource = Courses;
            UsersDataGrid.ItemsSource = Users;
            RecentPaymentsDataGrid.ItemsSource = RecentPayments;

            LoadAdminData();
        }

        private async void LoadAdminData()
        {
            await LoadCourses();
            await LoadUsers();
            await LoadReports();
        }

        private async Task LoadCourses()
        {
            try
            {
                using var context = new AppDbContext();
                var courses = await context.Courses
                    .OrderBy(c => c.Level)
                    .ThenBy(c => c.OrderIndex)
                    .ToListAsync();

                Courses.Clear();
                foreach (var course in courses)
                {
                    Courses.Add(course);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Dersler yüklenirken hata oluştu: {ex.Message}",
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadUsers()
        {
            try
            {
                using var context = new AppDbContext();
                var users = await context.Users
                    .OrderByDescending(u => u.CreatedDate)
                    .ToListAsync();

                Users.Clear();
                foreach (var user in users)
                {
                    Users.Add(user);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Kullanıcılar yüklenirken hata oluştu: {ex.Message}",
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadReports()
        {
            try
            {
                using var context = new AppDbContext();

                // İstatistikler
                var totalUsers = await context.Users.CountAsync();
                var totalCourses = await context.Courses.CountAsync();
                var totalRevenue = await context.Payments
                    .Where(p => p.Status == PaymentStatus.Completed)
                    .SumAsync(p => p.Amount);
                var totalSales = await context.Payments
                    .Where(p => p.Status == PaymentStatus.Completed)
                    .CountAsync();

                TotalUsersTextBlock.Text = totalUsers.ToString();
                TotalCoursesReportTextBlock.Text = totalCourses.ToString();
                TotalRevenueTextBlock.Text = $"₺{totalRevenue:N2}";
                TotalSalesTextBlock.Text = totalSales.ToString();

                // Son ödemeler
                var recentPayments = await context.Payments
                    .Include(p => p.User)
                    .Include(p => p.Course)
                    .Where(p => p.Status == PaymentStatus.Completed)
                    .OrderByDescending(p => p.PaymentDate)
                    .Take(10)
                    .ToListAsync();

                RecentPayments.Clear();
                foreach (var payment in recentPayments)
                {
                    RecentPayments.Add(payment);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Raporlar yüklenirken hata oluştu: {ex.Message}",
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ManageCoursesButton_Click(object sender, RoutedEventArgs e)
        {
            // Ders yönetimi sekmesine geç - bu kısım UI'da TabControl bulunmadığı için yorum satırı
            MessageBox.Show("Ders yönetimi özelliği aktif!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ManageUsersButton_Click(object sender, RoutedEventArgs e)
        {
            // Kullanıcı yönetimi sekmesine geç
            MessageBox.Show("Kullanıcı yönetimi özelliği aktif!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ViewReportsButton_Click(object sender, RoutedEventArgs e)
        {
            // Raporlar sekmesine geç
            MessageBox.Show("Raporlar özelliği aktif!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void AddCourseButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateCourseForm()) return;

            try
            {
                AddCourseButton.IsEnabled = false;
                AddCourseButton.Content = "Ekleniyor...";

                using var context = new AppDbContext();

                var course = new Course
                {
                    Title = CourseTitleTextBox.Text.Trim(),
                    Description = CourseDescriptionTextBox.Text.Trim(),
                    Content = "Bu ders içeriği admin tarafından eklenecektir...", // Varsayılan içerik
                    Level = GetSelectedLevel(),
                    Price = decimal.Parse(CoursePriceTextBox.Text),
                    EstimatedDurationMinutes = int.Parse(CourseDurationTextBox.Text),
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    OrderIndex = await context.Courses.CountAsync() + 1
                };

                context.Courses.Add(course);
                await context.SaveChangesAsync();

                MessageBox.Show("Ders başarıyla eklendi!", "Başarılı", 
                              MessageBoxButton.OK, MessageBoxImage.Information);

                // Formu temizle
                ClearCourseForm();
                
                // Listeyi yenile
                await LoadCourses();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ders eklenirken hata oluştu: {ex.Message}",
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                AddCourseButton.IsEnabled = true;
                AddCourseButton.Content = "Ders Ekle";
            }
        }

        private void EditCourseButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int courseId)
            {
                MessageBox.Show($"Ders düzenleme özelliği (ID: {courseId}) geliştirilecek!", 
                              "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void DeleteCourseButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int courseId)
            {
                var result = MessageBox.Show("Bu dersi silmek istediğinizden emin misiniz?",
                                           "Silme Onayı", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        using var context = new AppDbContext();
                        var course = await context.Courses.FindAsync(courseId);
                        
                        if (course != null)
                        {
                            context.Courses.Remove(course);
                            await context.SaveChangesAsync();

                            MessageBox.Show("Ders başarıyla silindi!", "Başarılı",
                                          MessageBoxButton.OK, MessageBoxImage.Information);

                            await LoadCourses();
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Ders silinirken hata oluştu: {ex.Message}",
                                      "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private bool ValidateCourseForm()
        {
            if (string.IsNullOrWhiteSpace(CourseTitleTextBox.Text))
            {
                MessageBox.Show("Lütfen ders adını girin.", "Uyarı", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(CoursePriceTextBox.Text) || !decimal.TryParse(CoursePriceTextBox.Text, out _))
            {
                MessageBox.Show("Lütfen geçerli bir fiyat girin.", "Uyarı", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (CourseLevelComboBox.SelectedItem == null)
            {
                MessageBox.Show("Lütfen ders seviyesini seçin.", "Uyarı", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(CourseDurationTextBox.Text) || !int.TryParse(CourseDurationTextBox.Text, out _))
            {
                MessageBox.Show("Lütfen geçerli bir süre girin.", "Uyarı", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private CourseLevel GetSelectedLevel()
        {
            if (CourseLevelComboBox.SelectedItem is ComboBoxItem item)
            {
                return item.Tag.ToString() switch
                {
                    "Beginner" => CourseLevel.Beginner,
                    "Intermediate" => CourseLevel.Intermediate,
                    "Advanced" => CourseLevel.Advanced,
                    _ => CourseLevel.Beginner
                };
            }
            return CourseLevel.Beginner;
        }

        private void ClearCourseForm()
        {
            CourseTitleTextBox.Text = "";
            CourseDescriptionTextBox.Text = "";
            CoursePriceTextBox.Text = "";
            CourseDurationTextBox.Text = "";
            CourseLevelComboBox.SelectedIndex = -1;
        }
    }
}
