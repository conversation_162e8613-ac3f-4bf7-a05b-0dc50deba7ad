using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using JavaOtomasyonApp.Views.Windows;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Pages
{
    public partial class ProgressPage : Page
    {
        public ObservableCollection<UserProgress> UserProgresses { get; set; }

        public ProgressPage()
        {
            InitializeComponent();
            UserProgresses = new ObservableCollection<UserProgress>();
            ProgressItemsControl.ItemsSource = UserProgresses;
            LoadProgressData();
        }

        private async void LoadProgressData()
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                using var context = new AppDbContext();
                var userId = AuthenticationService.CurrentUser.Id;

                // Kullanıcının satın aldığı derslerin ilerlemelerini getir
                var progresses = await context.UserProgresses
                    .Where(up => up.UserId == userId)
                    .Include(up => up.Course)
                    .OrderByDescending(up => up.LastAccessedDate)
                    .ToListAsync();

                UserProgresses.Clear();
                foreach (var progress in progresses)
                {
                    UserProgresses.Add(progress);
                }

                // İstatistikleri hesapla ve göster
                UpdateStatistics(progresses);

                // Eğer hiç ders yoksa mesaj göster
                NoProgressMessage.Visibility = progresses.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"İlerleme verileri yüklenirken hata oluştu: {ex.Message}",
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics(List<UserProgress> progresses)
        {
            if (progresses.Count == 0)
            {
                OverallProgressTextBlock.Text = "0%";
                CompletedCoursesTextBlock.Text = "0 / 0";
                TotalStudyTimeTextBlock.Text = "0 saat";
                return;
            }

            // Genel ilerleme yüzdesi (tüm derslerin ortalama ilerlemesi)
            var overallProgress = progresses.Average(p => p.ProgressPercentage);
            OverallProgressTextBlock.Text = $"{overallProgress:F0}%";

            // Tamamlanan ders sayısı
            var completedCount = progresses.Count(p => p.Status == ProgressStatus.Completed);
            var totalCount = progresses.Count;
            CompletedCoursesTextBlock.Text = $"{completedCount} / {totalCount}";

            // Toplam çalışma süresi
            var totalMinutes = progresses.Sum(p => p.TimeSpentMinutes);
            TotalStudyTimeTextBlock.Text = FormatStudyTime(totalMinutes);
        }

        private string FormatStudyTime(int totalMinutes)
        {
            if (totalMinutes < 60)
                return $"{totalMinutes} dakika";

            var hours = totalMinutes / 60;
            var minutes = totalMinutes % 60;

            if (minutes == 0)
                return $"{hours} saat";

            return $"{hours} saat {minutes} dakika";
        }

        private async void ContinueButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int courseId)
            {
                try
                {
                    if (AuthenticationService.CurrentUser == null) return;

                    using var context = new AppDbContext();
                    var userProgress = await context.UserProgresses
                        .Include(up => up.Course)
                        .FirstOrDefaultAsync(up => up.UserId == AuthenticationService.CurrentUser.Id && 
                                                 up.CourseId == courseId);

                    if (userProgress != null)
                    {
                        // İlerlemeyi güncelle
                        if (userProgress.Status == ProgressStatus.NotStarted)
                        {
                            userProgress.Status = ProgressStatus.InProgress;
                            userProgress.StartedDate = DateTime.Now;
                        }

                        userProgress.LastAccessedDate = DateTime.Now;
                        
                        // Simülasyon: İlerleme yüzdesini artır
                        if (userProgress.ProgressPercentage < 100)
                        {
                            userProgress.ProgressPercentage = Math.Min(100, userProgress.ProgressPercentage + 10);
                            userProgress.TimeSpentMinutes += 15; // 15 dakika ekleme simülasyonu
                            
                            if (userProgress.ProgressPercentage >= 100)
                            {
                                userProgress.Status = ProgressStatus.Completed;
                                userProgress.CompletedDate = DateTime.Now;
                            }
                        }

                        await context.SaveChangesAsync();

                        MessageBox.Show(
                            $"'{userProgress.Course.Title}' dersinde ilerleme kaydedildi!\n\nYeni ilerleme: %{userProgress.ProgressPercentage}",
                            "İlerleme Kaydedildi",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);

                        // Sayfayı yenile
                        LoadProgressData();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"İlerleme güncellenirken hata oluştu: {ex.Message}",
                                  "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void DetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int courseId)
            {
                try
                {
                    using var context = new AppDbContext();
                    var course = await context.Courses.FindAsync(courseId);
                    
                    if (course != null)
                    {
                        var detailWindow = new CourseDetailWindow(course);
                        detailWindow.Owner = Window.GetWindow(this);
                        detailWindow.ShowDialog();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Ders detayları yüklenirken hata oluştu: {ex.Message}",
                                  "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ViewCoursesButton_Click(object sender, RoutedEventArgs e)
        {
            // Ana penceredeki Dersler sayfasına yönlendir
            if (Window.GetWindow(this) is MainWindow mainWindow)
            {
                mainWindow.NavigateToCoursesPage();
            }
        }
    }
}
