<Window x:Class="JavaOtomasyonApp.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Java Öğrenme Otomasyonu - Giriş"
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="20">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="☕" FontSize="48" HorizontalAlignment="Center" Foreground="White"/>
                <TextBlock Text="Java Öğrenme Platformu" 
                          FontSize="18" FontWeight="Bold" 
                          HorizontalAlignment="Center" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Login Form -->
        <Border Grid.Row="1" Background="White" Margin="20" CornerRadius="10">
            <StackPanel Margin="30" VerticalAlignment="Center">
                <TextBlock Text="Giriş Yap" Style="{StaticResource TitleTextStyle}" 
                          HorizontalAlignment="Center"/>

                <TextBlock Text="E-posta:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="EmailTextBox" Style="{StaticResource ModernTextBoxStyle}"
                        Margin="0,0,0,15"/>

                <TextBlock Text="Şifre:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <PasswordBox x:Name="PasswordBox" 
                            Padding="10,8" FontSize="14" BorderThickness="1"
                            BorderBrush="#CCCCCC" Margin="0,0,0,20"/>

                <Button x:Name="LoginButton" Content="Giriş Yap" 
                       Style="{StaticResource ModernButtonStyle}"
                       Click="LoginButton_Click" Margin="0,0,0,10"/>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10">
                    <TextBlock Text="Hesabınız yok mu? " VerticalAlignment="Center"/>
                    <Button x:Name="RegisterButton" Content="Kayıt Ol" 
                           Background="Transparent" BorderThickness="0"
                           Foreground="#007ACC" FontWeight="SemiBold"
                           Cursor="Hand" Click="RegisterButton_Click"/>
                </StackPanel>

                <Button x:Name="ForgotPasswordButton" Content="Şifremi Unuttum" 
                       Background="Transparent" BorderThickness="0"
                       Foreground="#666666" FontSize="12"
                       HorizontalAlignment="Center" Cursor="Hand"
                       Click="ForgotPasswordButton_Click" Margin="0,5"/>

                <TextBlock x:Name="MessageTextBlock" 
                          TextWrapping="Wrap" HorizontalAlignment="Center"
                          Margin="0,15,0,0" FontSize="12"/>
            </StackPanel>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#F0F0F0" Padding="10">
            <TextBlock Text="© 2024 Java Öğrenme Otomasyonu" 
                      HorizontalAlignment="Center" FontSize="10" 
                      Foreground="#666666"/>
        </Border>
    </Grid>
</Window>
