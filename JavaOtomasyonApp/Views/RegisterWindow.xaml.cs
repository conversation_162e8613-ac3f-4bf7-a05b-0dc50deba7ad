using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Media;
using JavaOtomasyonApp.Services;

namespace JavaOtomasyonApp.Views
{
    public partial class RegisterWindow : Window
    {
        public RegisterWindow()
        {
            InitializeComponent();
        }

        private async void RegisterButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            RegisterButton.IsEnabled = false;
            RegisterButton.Content = "Kayıt yapılıyor...";

            try
            {
                var result = await AuthenticationService.RegisterAsync(
                    EmailTextBox.Text.Trim(),
                    PasswordBox.Password,
                    FirstNameTextBox.Text.Trim(),
                    LastNameTextBox.Text.Trim());

                if (result.Success)
                {
                    ShowMessage(result.Message, true);
                    
                    // 2 saniye bekle ve pencereyi kapat
                    await Task.Delay(2000);
                    this.DialogResult = true;
                    this.Close();
                }
                else
                {
                    ShowMessage(result.Message, false);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"Beklenmeyen hata: {ex.Message}", false);
            }
            finally
            {
                RegisterButton.IsEnabled = true;
                RegisterButton.Content = "Kayıt Ol";
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }

        private bool ValidateForm()
        {
            // Ad kontrolü
            if (string.IsNullOrWhiteSpace(FirstNameTextBox.Text))
            {
                ShowMessage("Lütfen adınızı girin.", false);
                FirstNameTextBox.Focus();
                return false;
            }

            // Soyad kontrolü
            if (string.IsNullOrWhiteSpace(LastNameTextBox.Text))
            {
                ShowMessage("Lütfen soyadınızı girin.", false);
                LastNameTextBox.Focus();
                return false;
            }

            // E-posta kontrolü
            if (string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                ShowMessage("Lütfen e-posta adresinizi girin.", false);
                EmailTextBox.Focus();
                return false;
            }

            if (!IsValidEmail(EmailTextBox.Text))
            {
                ShowMessage("Lütfen geçerli bir e-posta adresi girin.", false);
                EmailTextBox.Focus();
                return false;
            }

            // Şifre kontrolü
            if (string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                ShowMessage("Lütfen şifrenizi girin.", false);
                PasswordBox.Focus();
                return false;
            }

            if (PasswordBox.Password.Length < 6)
            {
                ShowMessage("Şifre en az 6 karakter olmalıdır.", false);
                PasswordBox.Focus();
                return false;
            }

            // Şifre tekrar kontrolü
            if (PasswordBox.Password != ConfirmPasswordBox.Password)
            {
                ShowMessage("Şifreler eşleşmiyor.", false);
                ConfirmPasswordBox.Focus();
                return false;
            }

            // Kullanım şartları kontrolü
            if (TermsCheckBox.IsChecked != true)
            {
                ShowMessage("Lütfen kullanım şartlarını kabul edin.", false);
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                return emailRegex.IsMatch(email);
            }
            catch
            {
                return false;
            }
        }

        private void ShowMessage(string message, bool isSuccess)
        {
            MessageTextBlock.Text = message;
            MessageTextBlock.Foreground = isSuccess ? 
                new SolidColorBrush(Colors.Green) : 
                new SolidColorBrush(Colors.Red);
        }
    }
}
