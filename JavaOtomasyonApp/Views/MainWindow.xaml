<Window x:Class="JavaOtomasyonApp.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Java Öğrenme Otomasyonu"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="#F8F9FA">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Top Menu Bar -->
        <Border Grid.Row="0" Grid.ColumnSpan="2" Background="#007ACC" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="☕" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="Java Öğrenme Platformu" 
                              FontSize="18" FontWeight="Bold" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock x:Name="WelcomeTextBlock" 
                              Foreground="White" FontSize="14" 
                              VerticalAlignment="Center" Margin="0,0,15,0"/>
                    
                    <Button x:Name="ProfileButton" Content="Profil" 
                           Background="Transparent" Foreground="White"
                           BorderBrush="White" BorderThickness="1"
                           Padding="10,5" Margin="0,0,10,0"
                           Click="ProfileButton_Click"/>
                    
                    <Button x:Name="LogoutButton" Content="Çıkış" 
                           Background="#DC3545" Foreground="White"
                           BorderThickness="0" Padding="10,5"
                           Click="LogoutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Left Navigation Panel -->
        <Border Grid.Row="1" Grid.Column="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0">
            <StackPanel Margin="0,20,0,0">
                <Button x:Name="DashboardButton" Content="📊 Dashboard" 
                       Style="{StaticResource NavigationButtonStyle}"
                       Click="DashboardButton_Click"/>
                
                <Button x:Name="CoursesButton" Content="📚 Dersler" 
                       Style="{StaticResource NavigationButtonStyle}"
                       Click="CoursesButton_Click"/>
                
                <Button x:Name="ProgressButton" Content="📈 İlerleme" 
                       Style="{StaticResource NavigationButtonStyle}"
                       Click="ProgressButton_Click"/>
                
                <Button x:Name="PaymentsButton" Content="💳 Ödemeler" 
                       Style="{StaticResource NavigationButtonStyle}"
                       Click="PaymentsButton_Click"/>
                
                <Separator Margin="10,20" Background="#E0E0E0"/>
                
                <Button x:Name="AdminButton" Content="⚙️ Admin Panel" 
                       Style="{StaticResource NavigationButtonStyle}"
                       Click="AdminButton_Click"
                       Visibility="Collapsed"/>
            </StackPanel>
        </Border>

        <!-- Main Content Area -->
        <Border Grid.Row="1" Grid.Column="1" Background="White" Margin="20">
            <Frame x:Name="MainFrame" NavigationUIVisibility="Hidden"/>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="2" Grid.ColumnSpan="2" Background="#F0F0F0" Padding="15,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="StatusTextBlock" 
                          Text="Hazır" FontSize="12" 
                          VerticalAlignment="Center"/>

                <TextBlock Grid.Column="1" 
                          Text="© 2024 Java Öğrenme Otomasyonu" 
                          FontSize="10" Foreground="#666666"
                          VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>

    <Window.Resources>
        <Style x:Key="NavigationButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F8F9FA"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#E9ECEF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
</Window>
