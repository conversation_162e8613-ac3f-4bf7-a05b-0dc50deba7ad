<Window x:Class="JavaOtomasyonApp.Views.Windows.ProfileWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Profil Ayarları"
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F8F9FA">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="30">
            <StackPanel Orientation="Horizontal">
                <Border Background="White" CornerRadius="50" 
                       Width="80" Height="80" Margin="0,0,20,0">
                    <TextBlock Text="👤" FontSize="32" 
                              Foreground="#007ACC" 
                              HorizontalAlignment="Center" 
                              VerticalAlignment="Center"/>
                </Border>
                
                <StackPanel VerticalAlignment="Center">
                    <TextBlock x:Name="UserNameTextBlock" 
                              FontSize="24" FontWeight="Bold" 
                              Foreground="White" Margin="0,0,0,5"/>
                    <TextBlock x:Name="UserEmailTextBlock" 
                              FontSize="14" Foreground="White"/>
                    <TextBlock x:Name="MemberSinceTextBlock" 
                              FontSize="12" Foreground="White" 
                              Opacity="0.8"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="30">
                <!-- Personal Information -->
                <Border Background="White" CornerRadius="10" 
                       Padding="25" Margin="0,0,0,20"
                       BorderBrush="#E0E0E0" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="Kişisel Bilgiler" Style="{StaticResource SubtitleTextStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Ad:" 
                                      FontWeight="SemiBold" Margin="0,0,10,5"/>
                            <TextBox Grid.Row="1" Grid.Column="0" x:Name="FirstNameTextBox" 
                                    Style="{StaticResource ModernTextBoxStyle}" 
                                    Margin="0,0,10,15"/>

                            <TextBlock Grid.Row="0" Grid.Column="1" Text="Soyad:" 
                                      FontWeight="SemiBold" Margin="10,0,0,5"/>
                            <TextBox Grid.Row="1" Grid.Column="1" x:Name="LastNameTextBox" 
                                    Style="{StaticResource ModernTextBoxStyle}" 
                                    Margin="10,0,0,15"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" 
                                      Text="E-posta:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" 
                                    x:Name="EmailTextBox" 
                                    Style="{StaticResource ModernTextBoxStyle}" 
                                    IsReadOnly="True" Background="#F8F9FA"/>
                        </Grid>

                        <Button x:Name="UpdateProfileButton" Content="Profili Güncelle" 
                               Style="{StaticResource ModernButtonStyle}"
                               HorizontalAlignment="Left" Margin="0,10,0,0"
                               Click="UpdateProfileButton_Click"/>
                    </StackPanel>
                </Border>

                <!-- Change Password -->
                <Border Background="White" CornerRadius="10" 
                       Padding="25" Margin="0,0,0,20"
                       BorderBrush="#E0E0E0" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="Şifre Değiştir" Style="{StaticResource SubtitleTextStyle}"/>
                        
                        <TextBlock Text="Mevcut Şifre:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <PasswordBox x:Name="CurrentPasswordBox" 
                                    Padding="10,8" FontSize="14" BorderThickness="1"
                                    BorderBrush="#CCCCCC" Margin="0,0,0,15"/>

                        <TextBlock Text="Yeni Şifre:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <PasswordBox x:Name="NewPasswordBox" 
                                    Padding="10,8" FontSize="14" BorderThickness="1"
                                    BorderBrush="#CCCCCC" Margin="0,0,0,15"/>

                        <TextBlock Text="Yeni Şifre Tekrar:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <PasswordBox x:Name="ConfirmNewPasswordBox" 
                                    Padding="10,8" FontSize="14" BorderThickness="1"
                                    BorderBrush="#CCCCCC" Margin="0,0,0,15"/>

                        <Button x:Name="ChangePasswordButton" Content="Şifreyi Değiştir" 
                               Background="#FFC107" Foreground="White"
                               BorderThickness="0" Padding="15,8" FontSize="14"
                               Cursor="Hand" HorizontalAlignment="Left"
                               Click="ChangePasswordButton_Click"/>
                    </StackPanel>
                </Border>

                <!-- Account Statistics -->
                <Border Background="White" CornerRadius="10" 
                       Padding="25" BorderBrush="#E0E0E0" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="Hesap İstatistikleri" Style="{StaticResource SubtitleTextStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Text="📚" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock x:Name="TotalCoursesStatTextBlock" Text="0" 
                                          FontSize="20" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Margin="0,0,0,2"/>
                                <TextBlock Text="Satın Alınan Ders" FontSize="12" 
                                          Foreground="#666666" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock x:Name="CompletedCoursesStatTextBlock" Text="0" 
                                          FontSize="20" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Margin="0,0,0,2"/>
                                <TextBlock Text="Tamamlanan Ders" FontSize="12" 
                                          Foreground="#666666" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock x:Name="TotalSpentStatTextBlock" Text="₺0" 
                                          FontSize="20" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Margin="0,0,0,2"/>
                                <TextBlock Text="Toplam Harcama" FontSize="12" 
                                          Foreground="#666666" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Message Area -->
                <TextBlock x:Name="MessageTextBlock" 
                          TextWrapping="Wrap" HorizontalAlignment="Center"
                          Margin="0,15,0,0" FontSize="12"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="White" Padding="30" 
               BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <Button x:Name="CloseButton" Content="Kapat" 
                   Background="#6C757D" Foreground="White"
                   BorderThickness="0" Padding="20,10" FontSize="14"
                   Cursor="Hand" HorizontalAlignment="Right"
                   Click="CloseButton_Click"/>
        </Border>
    </Grid>
</Window>
