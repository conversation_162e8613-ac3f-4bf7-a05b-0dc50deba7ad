using System.Windows;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Windows
{
    public partial class CourseDetailWindow : Window
    {
        private readonly Course _course;
        private bool _isPurchased = false;

        public CourseDetailWindow(Course course)
        {
            InitializeComponent();
            _course = course;
            LoadCourseDetails();
            CheckPurchaseStatus();
        }

        private void LoadCourseDetails()
        {
            TitleTextBlock.Text = _course.Title;
            LevelTextBlock.Text = _course.LevelDisplayName;
            DurationTextBlock.Text = _course.EstimatedDuration;
            PriceTextBlock.Text = _course.FormattedPrice;
            DescriptionTextBlock.Text = _course.Description;
            ContentTextBlock.Text = _course.Content;

            // Ön koşullar varsa göster
            if (!string.IsNullOrWhiteSpace(_course.Prerequisites))
            {
                PrerequisitesTextBlock.Text = _course.Prerequisites;
                PrerequisitesBorder.Visibility = Visibility.Visible;
            }
            else
            {
                PrerequisitesBorder.Visibility = Visibility.Collapsed;
            }

            this.Title = $"Ders Detayları - {_course.Title}";
        }

        private async void CheckPurchaseStatus()
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                using var context = new AppDbContext();
                var payment = await context.Payments
                    .FirstOrDefaultAsync(p => p.UserId == AuthenticationService.CurrentUser.Id && 
                                            p.CourseId == _course.Id && 
                                            p.Status == PaymentStatus.Completed);

                if (payment != null)
                {
                    _isPurchased = true;
                    PurchaseStatusBorder.Visibility = Visibility.Visible;
                    PurchaseInfoTextBlock.Text = $"Satın alma tarihi: {payment.CompletedDate:dd.MM.yyyy HH:mm}";
                    
                    PurchaseButton.Visibility = Visibility.Collapsed;
                    StartCourseButton.Visibility = Visibility.Visible;
                }
                else
                {
                    _isPurchased = false;
                    PurchaseStatusBorder.Visibility = Visibility.Collapsed;
                    PurchaseButton.Visibility = Visibility.Visible;
                    StartCourseButton.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Satın alma durumu kontrol edilirken hata oluştu: {ex.Message}",
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void PurchaseButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                $"'{_course.Title}' dersini {_course.FormattedPrice} karşılığında satın almak istediğinizden emin misiniz?",
                "Satın Alma Onayı",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await ProcessPurchase();
            }
        }

        private async Task ProcessPurchase()
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                PurchaseButton.IsEnabled = false;
                PurchaseButton.Content = "İşleniyor...";

                using var context = new AppDbContext();

                // Ödeme kaydı oluştur
                var payment = new Payment
                {
                    UserId = AuthenticationService.CurrentUser.Id,
                    CourseId = _course.Id,
                    Amount = _course.Price,
                    Status = PaymentStatus.Completed, // Simülasyon için direkt tamamlandı
                    PaymentDate = DateTime.Now,
                    CompletedDate = DateTime.Now,
                    TransactionId = Guid.NewGuid().ToString("N")[..10].ToUpper(),
                    PaymentMethod = "CreditCard",
                    Notes = "Simülasyon ödemesi"
                };

                context.Payments.Add(payment);

                // Kullanıcı ilerleme kaydı oluştur
                var userProgress = new UserProgress
                {
                    UserId = AuthenticationService.CurrentUser.Id,
                    CourseId = _course.Id,
                    Status = ProgressStatus.NotStarted,
                    ProgressPercentage = 0,
                    StartedDate = null,
                    CompletedDate = null,
                    LastAccessedDate = DateTime.Now,
                    TimeSpentMinutes = 0
                };

                context.UserProgresses.Add(userProgress);
                await context.SaveChangesAsync();

                MessageBox.Show(
                    $"'{_course.Title}' dersi başarıyla satın alındı!\n\nİşlem No: {payment.TransactionId}\nTutar: {payment.FormattedAmount}",
                    "Satın Alma Başarılı",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                // UI'yi güncelle
                CheckPurchaseStatus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Satın alma işlemi sırasında hata oluştu: {ex.Message}",
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                PurchaseButton.IsEnabled = true;
                PurchaseButton.Content = "Satın Al";
            }
        }

        private async void StartCourseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                using var context = new AppDbContext();
                var userProgress = await context.UserProgresses
                    .FirstOrDefaultAsync(up => up.UserId == AuthenticationService.CurrentUser.Id && 
                                             up.CourseId == _course.Id);

                if (userProgress != null)
                {
                    // İlerlemeyi güncelle
                    if (userProgress.Status == ProgressStatus.NotStarted)
                    {
                        userProgress.Status = ProgressStatus.InProgress;
                        userProgress.StartedDate = DateTime.Now;
                    }
                    
                    userProgress.LastAccessedDate = DateTime.Now;
                    await context.SaveChangesAsync();

                    MessageBox.Show(
                        $"'{_course.Title}' dersine başladınız!\n\nDers içeriğini inceleyebilir ve öğrenmeye başlayabilirsiniz.",
                        "Derse Başlandı",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    // Burada gerçek uygulamada ders içeriği sayfasına yönlendirme yapılabilir
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Derse başlarken hata oluştu: {ex.Message}",
                              "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
