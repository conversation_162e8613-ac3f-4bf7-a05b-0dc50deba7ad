<Window x:Class="JavaOtomasyonApp.Views.Windows.CourseDetailWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Ders Detayları"
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="#F8F9FA">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="30">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <Border Grid.Column="0" Background="White" CornerRadius="50" 
                       Width="80" Height="80" Margin="0,0,20,0">
                    <TextBlock Text="☕" FontSize="32" 
                              Foreground="#007ACC" 
                              HorizontalAlignment="Center" 
                              VerticalAlignment="Center"/>
                </Border>

                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock x:Name="TitleTextBlock" 
                              FontSize="24" FontWeight="Bold" 
                              Foreground="White" Margin="0,0,0,5"/>
                    
                    <StackPanel Orientation="Horizontal">
                        <Border Background="White" CornerRadius="12" 
                               Padding="8,4" Margin="0,0,10,0">
                            <TextBlock x:Name="LevelTextBlock" 
                                      Foreground="#007ACC" FontSize="12" FontWeight="Bold"/>
                        </Border>
                        <Border Background="White" CornerRadius="12" 
                               Padding="8,4" Margin="0,0,10,0">
                            <TextBlock x:Name="DurationTextBlock" 
                                      Foreground="#007ACC" FontSize="12"/>
                        </Border>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="2" VerticalAlignment="Center">
                    <TextBlock x:Name="PriceTextBlock" 
                              FontSize="28" FontWeight="Bold" 
                              Foreground="White" HorizontalAlignment="Right"/>
                    <TextBlock Text="Fiyat" 
                              FontSize="12" Foreground="White" 
                              HorizontalAlignment="Right"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="30">
                <!-- Description -->
                <Border Background="White" CornerRadius="10" 
                       Padding="25" Margin="0,0,0,20"
                       BorderBrush="#E0E0E0" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="Ders Açıklaması" 
                                  Style="{StaticResource SubtitleTextStyle}"/>
                        <TextBlock x:Name="DescriptionTextBlock" 
                                  FontSize="14" TextWrapping="Wrap" 
                                  LineHeight="22"/>
                    </StackPanel>
                </Border>

                <!-- Prerequisites -->
                <Border x:Name="PrerequisitesBorder" Background="White" CornerRadius="10" 
                       Padding="25" Margin="0,0,0,20"
                       BorderBrush="#E0E0E0" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="Ön Koşullar" 
                                  Style="{StaticResource SubtitleTextStyle}"/>
                        <TextBlock x:Name="PrerequisitesTextBlock" 
                                  FontSize="14" TextWrapping="Wrap" 
                                  Foreground="#666666" FontStyle="Italic"/>
                    </StackPanel>
                </Border>

                <!-- Course Content -->
                <Border Background="White" CornerRadius="10" 
                       Padding="25" Margin="0,0,0,20"
                       BorderBrush="#E0E0E0" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="Ders İçeriği" 
                                  Style="{StaticResource SubtitleTextStyle}"/>
                        <ScrollViewer Height="200" VerticalScrollBarVisibility="Auto">
                            <TextBlock x:Name="ContentTextBlock" 
                                      FontSize="14" TextWrapping="Wrap" 
                                      LineHeight="20"/>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Purchase Status -->
                <Border x:Name="PurchaseStatusBorder" Background="#D4EDDA" 
                       CornerRadius="10" Padding="20" Margin="0,0,0,20"
                       BorderBrush="#C3E6CB" BorderThickness="1"
                       Visibility="Collapsed">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="✅" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <StackPanel>
                            <TextBlock Text="Bu dersi satın almışsınız!" 
                                      FontWeight="Bold" Foreground="#155724"/>
                            <TextBlock x:Name="PurchaseInfoTextBlock" 
                                      FontSize="12" Foreground="#155724"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="White" Padding="30" 
               BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="StartCourseButton" Content="Derse Başla" 
                       Background="#28A745" Foreground="White"
                       BorderThickness="0" Padding="20,10" FontSize="14"
                       Cursor="Hand" Margin="0,0,15,0"
                       Visibility="Collapsed"
                       Click="StartCourseButton_Click"/>
                
                <Button x:Name="PurchaseButton" Content="Satın Al" 
                       Style="{StaticResource ModernButtonStyle}"
                       Padding="20,10" Margin="0,0,15,0"
                       Click="PurchaseButton_Click"/>
                
                <Button x:Name="CloseButton" Content="Kapat" 
                       Background="#6C757D" Foreground="White"
                       BorderThickness="0" Padding="20,10" FontSize="14"
                       Cursor="Hand" Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
