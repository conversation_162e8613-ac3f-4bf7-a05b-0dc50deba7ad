using System.Windows;
using System.Windows.Media;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Windows
{
    public partial class ProfileWindow : Window
    {
        public ProfileWindow()
        {
            InitializeComponent();
            LoadUserProfile();
            LoadUserStatistics();
        }

        private void LoadUserProfile()
        {
            if (AuthenticationService.CurrentUser == null) return;

            var user = AuthenticationService.CurrentUser;
            
            UserNameTextBlock.Text = user.FullName;
            UserEmailTextBlock.Text = user.Email;
            MemberSinceTextBlock.Text = $"Üye olma tarihi: {user.CreatedDate:dd.MM.yyyy}";

            FirstNameTextBox.Text = user.FirstName ?? "";
            LastNameTextBox.Text = user.LastName ?? "";
            EmailTextBox.Text = user.Email;
        }

        private async void LoadUserStatistics()
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                using var context = new AppDbContext();
                var userId = AuthenticationService.CurrentUser.Id;

                // Satın alınan ders sayısı
                var purchasedCourses = await context.Payments
                    .Where(p => p.UserId == userId && p.Status == PaymentStatus.Completed)
                    .Select(p => p.CourseId)
                    .Distinct()
                    .CountAsync();

                // Tamamlanan ders sayısı
                var completedCourses = await context.UserProgresses
                    .Where(up => up.UserId == userId && up.Status == ProgressStatus.Completed)
                    .CountAsync();

                // Toplam harcama
                var totalSpent = await context.Payments
                    .Where(p => p.UserId == userId && p.Status == PaymentStatus.Completed)
                    .SumAsync(p => p.Amount);

                TotalCoursesStatTextBlock.Text = purchasedCourses.ToString();
                CompletedCoursesStatTextBlock.Text = completedCourses.ToString();
                TotalSpentStatTextBlock.Text = $"₺{totalSpent:N2}";
            }
            catch (Exception ex)
            {
                ShowMessage($"İstatistikler yüklenirken hata oluştu: {ex.Message}", false);
            }
        }

        private async void UpdateProfileButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateProfileForm()) return;

            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                UpdateProfileButton.IsEnabled = false;
                UpdateProfileButton.Content = "Güncelleniyor...";

                using var context = new AppDbContext();
                var user = await context.Users.FindAsync(AuthenticationService.CurrentUser.Id);
                
                if (user != null)
                {
                    user.FirstName = FirstNameTextBox.Text.Trim();
                    user.LastName = LastNameTextBox.Text.Trim();
                    
                    await context.SaveChangesAsync();

                    // Mevcut kullanıcı bilgilerini güncelle
                    AuthenticationService.CurrentUser.FirstName = user.FirstName;
                    AuthenticationService.CurrentUser.LastName = user.LastName;

                    ShowMessage("Profil başarıyla güncellendi!", true);
                    LoadUserProfile(); // UI'yi yenile
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"Profil güncellenirken hata oluştu: {ex.Message}", false);
            }
            finally
            {
                UpdateProfileButton.IsEnabled = true;
                UpdateProfileButton.Content = "Profili Güncelle";
            }
        }

        private async void ChangePasswordButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidatePasswordForm()) return;

            try
            {
                ChangePasswordButton.IsEnabled = false;
                ChangePasswordButton.Content = "Değiştiriliyor...";

                var result = await AuthenticationService.ChangePasswordAsync(
                    CurrentPasswordBox.Password,
                    NewPasswordBox.Password);

                if (result.Success)
                {
                    ShowMessage(result.Message, true);
                    
                    // Şifre alanlarını temizle
                    CurrentPasswordBox.Password = "";
                    NewPasswordBox.Password = "";
                    ConfirmNewPasswordBox.Password = "";
                }
                else
                {
                    ShowMessage(result.Message, false);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"Şifre değiştirilirken hata oluştu: {ex.Message}", false);
            }
            finally
            {
                ChangePasswordButton.IsEnabled = true;
                ChangePasswordButton.Content = "Şifreyi Değiştir";
            }
        }

        private bool ValidateProfileForm()
        {
            if (string.IsNullOrWhiteSpace(FirstNameTextBox.Text))
            {
                ShowMessage("Lütfen adınızı girin.", false);
                FirstNameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(LastNameTextBox.Text))
            {
                ShowMessage("Lütfen soyadınızı girin.", false);
                LastNameTextBox.Focus();
                return false;
            }

            return true;
        }

        private bool ValidatePasswordForm()
        {
            if (string.IsNullOrWhiteSpace(CurrentPasswordBox.Password))
            {
                ShowMessage("Lütfen mevcut şifrenizi girin.", false);
                CurrentPasswordBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(NewPasswordBox.Password))
            {
                ShowMessage("Lütfen yeni şifrenizi girin.", false);
                NewPasswordBox.Focus();
                return false;
            }

            if (NewPasswordBox.Password.Length < 6)
            {
                ShowMessage("Yeni şifre en az 6 karakter olmalıdır.", false);
                NewPasswordBox.Focus();
                return false;
            }

            if (NewPasswordBox.Password != ConfirmNewPasswordBox.Password)
            {
                ShowMessage("Yeni şifreler eşleşmiyor.", false);
                ConfirmNewPasswordBox.Focus();
                return false;
            }

            return true;
        }

        private void ShowMessage(string message, bool isSuccess)
        {
            MessageTextBlock.Text = message;
            MessageTextBlock.Foreground = isSuccess ? 
                new SolidColorBrush(Colors.Green) : 
                new SolidColorBrush(Colors.Red);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
