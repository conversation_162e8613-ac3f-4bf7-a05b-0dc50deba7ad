<Window x:Class="JavaOtomasyonApp.Views.RegisterWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Kayıt Ol - Java Öğrenme Otomasyonu"
        Height="600" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="20">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="☕" FontSize="48" HorizontalAlignment="Center" Foreground="White"/>
                <TextBlock Text="Yeni He<PERSON>tur" 
                          FontSize="18" FontWeight="Bold" 
                          HorizontalAlignment="Center" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Register Form -->
        <Border Grid.Row="1" Background="White" Margin="20" CornerRadius="10">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="30" VerticalAlignment="Center">
                    <TextBlock Text="Kayıt Formu" Style="{StaticResource TitleTextStyle}" 
                              HorizontalAlignment="Center"/>

                    <TextBlock Text="Ad:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="FirstNameTextBox" Style="{StaticResource ModernTextBoxStyle}"
                            Margin="0,0,0,15"/>

                    <TextBlock Text="Soyad:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="LastNameTextBox" Style="{StaticResource ModernTextBoxStyle}"
                            Margin="0,0,0,15"/>

                    <TextBlock Text="E-posta:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="EmailTextBox" Style="{StaticResource ModernTextBoxStyle}"
                            Margin="0,0,0,15"/>

                    <TextBlock Text="Şifre:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <PasswordBox x:Name="PasswordBox" 
                                Padding="10,8" FontSize="14" BorderThickness="1"
                                BorderBrush="#CCCCCC" Margin="0,0,0,15"/>

                    <TextBlock Text="Şifre Tekrar:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <PasswordBox x:Name="ConfirmPasswordBox" 
                                Padding="10,8" FontSize="14" BorderThickness="1"
                                BorderBrush="#CCCCCC" Margin="0,0,0,20"/>

                    <CheckBox x:Name="TermsCheckBox" Margin="0,0,0,20">
                        <TextBlock TextWrapping="Wrap">
                            <Run Text="Kullanım şartlarını ve gizlilik politikasını okudum, kabul ediyorum."/>
                        </TextBlock>
                    </CheckBox>

                    <Button x:Name="RegisterButton" Content="Kayıt Ol" 
                           Style="{StaticResource ModernButtonStyle}"
                           Click="RegisterButton_Click" Margin="0,0,0,10"/>

                    <Button x:Name="CancelButton" Content="İptal" 
                           Background="#6C757D" Foreground="White"
                           BorderThickness="0" Padding="15,8" FontSize="14"
                           Cursor="Hand" Click="CancelButton_Click"/>

                    <TextBlock x:Name="MessageTextBlock" 
                              TextWrapping="Wrap" HorizontalAlignment="Center"
                              Margin="0,15,0,0" FontSize="12"/>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#F0F0F0" Padding="10">
            <TextBlock Text="© 2024 Java Öğrenme Otomasyonu" 
                      HorizontalAlignment="Center" FontSize="10" 
                      Foreground="#666666"/>
        </Border>
    </Grid>
</Window>
