using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Services
{
    public class AuthenticationService
    {
        private static User? _currentUser;

        public static User? CurrentUser
        {
            get => _currentUser;
            private set => _currentUser = value;
        }

        public static bool IsLoggedIn => CurrentUser != null;
        public static bool IsAdmin => CurrentUser?.IsAdmin == true;

        public static async Task<(bool Success, string Message)> LoginAsync(string email, string password)
        {
            try
            {
                using var context = new AppDbContext();
                
                var user = await context.Users
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());

                if (user == null)
                {
                    return (false, "E-posta adresi bulunamadı.");
                }

                if (!user.IsActive)
                {
                    return (false, "Hesabınız deaktif durumda. Lütfen yönetici ile iletişime geçin.");
                }

                if (!BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                {
                    return (false, "<PERSON><PERSON>re hatalı.");
                }

                // Başarılı giriş
                user.LastLoginDate = DateTime.Now;
                await context.SaveChangesAsync();

                CurrentUser = user;
                return (true, "Giriş başarılı.");
            }
            catch (Exception ex)
            {
                return (false, $"Giriş sırasında hata oluştu: {ex.Message}");
            }
        }

        public static async Task<(bool Success, string Message)> RegisterAsync(
            string email, string password, string firstName, string lastName)
        {
            try
            {
                using var context = new AppDbContext();

                // E-posta kontrolü
                var existingUser = await context.Users
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());

                if (existingUser != null)
                {
                    return (false, "Bu e-posta adresi zaten kullanılıyor.");
                }

                // Şifre validasyonu
                if (password.Length < 6)
                {
                    return (false, "Şifre en az 6 karakter olmalıdır.");
                }

                // Yeni kullanıcı oluştur
                var newUser = new User
                {
                    Email = email.ToLower(),
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword(password),
                    FirstName = firstName,
                    LastName = lastName,
                    Role = "User",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                context.Users.Add(newUser);
                await context.SaveChangesAsync();

                return (true, "Kayıt başarılı. Şimdi giriş yapabilirsiniz.");
            }
            catch (Exception ex)
            {
                return (false, $"Kayıt sırasında hata oluştu: {ex.Message}");
            }
        }

        public static async Task<(bool Success, string Message)> ChangePasswordAsync(
            string currentPassword, string newPassword)
        {
            if (CurrentUser == null)
            {
                return (false, "Oturum açılmamış.");
            }

            try
            {
                using var context = new AppDbContext();
                
                var user = await context.Users.FindAsync(CurrentUser.Id);
                if (user == null)
                {
                    return (false, "Kullanıcı bulunamadı.");
                }

                if (!BCrypt.Net.BCrypt.Verify(currentPassword, user.PasswordHash))
                {
                    return (false, "Mevcut şifre hatalı.");
                }

                if (newPassword.Length < 6)
                {
                    return (false, "Yeni şifre en az 6 karakter olmalıdır.");
                }

                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
                await context.SaveChangesAsync();

                return (true, "Şifre başarıyla değiştirildi.");
            }
            catch (Exception ex)
            {
                return (false, $"Şifre değiştirme sırasında hata oluştu: {ex.Message}");
            }
        }

        public static async Task<(bool Success, string Message)> ResetPasswordAsync(string email)
        {
            try
            {
                using var context = new AppDbContext();
                
                var user = await context.Users
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());

                if (user == null)
                {
                    // Güvenlik için gerçek durumu söylemiyoruz
                    return (true, "Eğer bu e-posta adresi sistemde kayıtlıysa, şifre sıfırlama talimatları gönderildi.");
                }

                // Gerçek uygulamada burada e-posta gönderilir
                // Şimdilik geçici şifre oluşturup veritabanında güncelliyoruz
                var tempPassword = GenerateTemporaryPassword();
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(tempPassword);
                await context.SaveChangesAsync();

                // Geçici şifreyi kullanıcıya göster (gerçek uygulamada e-posta ile gönderilir)
                return (true, $"Geçici şifreniz: {tempPassword}\nLütfen giriş yaptıktan sonra şifrenizi değiştirin.");
            }
            catch (Exception ex)
            {
                return (false, $"Şifre sıfırlama sırasında hata oluştu: {ex.Message}");
            }
        }

        public static void Logout()
        {
            CurrentUser = null;
        }

        private static string GenerateTemporaryPassword()
        {
            var random = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            return new string(Enumerable.Repeat(chars, 8)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }
}
